#!/usr/bin/env node

/**
 * 自取订单状态显示测试脚本
 * 验证自取订单在"取货中"状态时显示"等待自取"
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证自取订单状态显示功能...\n')

// 检查文件内容是否包含指定字符串
const checkFileContains = (filePath, searchStrings, description) => {
  const fullPath = path.join(__dirname, filePath)
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${filePath}`)
    return false
  }

  const content = fs.readFileSync(fullPath, 'utf8')
  const results = searchStrings.map((str) => ({
    string: str,
    found: content.includes(str),
  }))

  const allFound = results.every((r) => r.found)
  if (allFound) {
    console.log(`✅ ${description}: ${filePath}`)
  } else {
    console.error(`❌ ${description}: ${filePath}`)
    results
      .filter((r) => !r.found)
      .forEach((r) => {
        console.error(`   缺少: ${r.string}`)
      })
  }
  return allFound
}

let allChecksPass = true

// 1. 检查核心文件是否存在
console.log('📁 检查核心文件...')
const coreFiles = ['src/pages/order/list.vue']

coreFiles.forEach((file) => {
  const fullPath = path.join(__dirname, file)
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${file}`)
    allChecksPass = false
  } else {
    console.log(`✅ 文件存在: ${file}`)
  }
})

console.log('')

// 2. 检查状态显示函数是否正确实现
console.log('🏪 检查自取订单状态显示功能...')
const statusChecks = [
  ['getDisplayStatusText', '状态显示函数'],
  ['order.delivery_type === 2', '自取订单判断'],
  ['order.status === 30', '订单状态判断'],
  ['order.delivery_status === 30', '取货状态判断'],
  ["return '等待自取'", '等待自取状态返回'],
  ['🏪 订单', '调试信息输出'],
]

statusChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/pages/order/list.vue', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 3. 检查模板是否使用新的状态显示函数
console.log('📄 检查模板更新...')
const templateChecks = [
  ['getDisplayStatusText(order as any)', '模板使用新的状态显示函数'],
  ['order-status', '订单状态样式类'],
]

templateChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/pages/order/list.vue', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 4. 模拟测试数据验证逻辑
console.log('🧪 模拟测试数据验证...')

// 模拟订单数据
const testOrders = [
  {
    id: 102,
    order_no: '202507312242578223',
    status: 30,
    status_text: '处理中',
    delivery_type: 2,
    delivery_type_text: '到店自取',
    delivery_status: 30,
    delivery_status_text: '取货中',
    expected: '等待自取',
    description: '自取订单-取货中状态'
  },
  {
    id: 103,
    order_no: '202507312242578224',
    status: 30,
    status_text: '处理中',
    delivery_type: 0,
    delivery_type_text: '配送',
    delivery_status: 30,
    delivery_status_text: '配送中',
    expected: '处理中',
    description: '配送订单-配送中状态'
  },
  {
    id: 104,
    order_no: '202507312242578225',
    status: 20,
    status_text: '待发货',
    delivery_type: 2,
    delivery_type_text: '到店自取',
    delivery_status: 10,
    delivery_status_text: '待取货',
    expected: '待发货',
    description: '自取订单-待发货状态'
  },
  {
    id: 105,
    order_no: '202507312242578226',
    status: 50,
    status_text: '已完成',
    delivery_type: 2,
    delivery_type_text: '到店自取',
    delivery_status: 50,
    delivery_status_text: '已取货',
    expected: '已完成',
    description: '自取订单-已完成状态'
  }
]

// 模拟状态显示逻辑
const mockGetDisplayStatusText = (order) => {
  // 如果是自取订单（delivery_type = 2）且订单状态为处理中（status = 30）且取货状态为取货中（delivery_status = 30）
  if (order.delivery_type === 2 && order.status === 30 && order.delivery_status === 30) {
    return '等待自取'
  }
  
  // 其他情况使用原有逻辑
  return order.status_text
}

let testsPassed = 0
let totalTests = testOrders.length

testOrders.forEach((order) => {
  const result = mockGetDisplayStatusText(order)
  const passed = result === order.expected
  
  if (passed) {
    console.log(`✅ ${order.description}: ${result}`)
    testsPassed++
  } else {
    console.error(`❌ ${order.description}: 期望 "${order.expected}", 实际 "${result}"`)
    allChecksPass = false
  }
})

console.log(`\n📊 测试结果: ${testsPassed}/${totalTests} 通过`)

console.log('')

// 总结
if (allChecksPass) {
  console.log('🎉 所有检查通过！自取订单状态显示功能实现正确。')
  console.log('')
  console.log('✨ 实现的功能:')
  console.log('  - ✅ 创建了 getDisplayStatusText 函数处理特殊状态显示')
  console.log('  - ✅ 当自取订单处于"取货中"状态时显示"等待自取"')
  console.log('  - ✅ 其他情况保持原有状态显示逻辑')
  console.log('  - ✅ 添加了详细的调试信息输出')
  console.log('  - ✅ 更新了模板使用新的状态显示函数')
  console.log('')
  console.log('🔄 状态显示规则:')
  console.log('  - 自取订单 + 订单状态30(处理中) + 取货状态30(取货中) → "等待自取"')
  console.log('  - 其他所有情况 → 使用原有状态文本')
  console.log('')
  console.log('🚀 建议下一步:')
  console.log('  1. 在开发环境中测试订单列表页面')
  console.log('  2. 创建一个自取订单并验证状态显示')
  console.log('  3. 检查控制台调试信息是否正确输出')
  console.log('  4. 验证其他类型订单的状态显示不受影响')
  process.exit(0)
} else {
  console.error('❌ 部分检查未通过，请检查上述错误并修复。')
  process.exit(1)
}
