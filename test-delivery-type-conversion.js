#!/usr/bin/env node

/**
 * 配送方式转换功能测试脚本
 * 验证deliveryType字段的转换是否正确工作
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证配送方式转换功能...\n')

// 检查文件是否存在
const checkFileExists = (filePath) => {
  const fullPath = path.join(__dirname, filePath)
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${filePath}`)
    return false
  }
  console.log(`✅ 文件存在: ${filePath}`)
  return true
}

// 检查文件内容是否包含指定字符串
const checkFileContains = (filePath, searchStrings, description) => {
  const fullPath = path.join(__dirname, filePath)
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${filePath}`)
    return false
  }

  const content = fs.readFileSync(fullPath, 'utf8')
  const results = searchStrings.map((str) => ({
    string: str,
    found: content.includes(str),
  }))

  const allFound = results.every((r) => r.found)
  if (allFound) {
    console.log(`✅ ${description}: ${filePath}`)
  } else {
    console.error(`❌ ${description}: ${filePath}`)
    results
      .filter((r) => !r.found)
      .forEach((r) => {
        console.error(`   缺少: ${r.string}`)
      })
  }
  return allFound
}

let allChecksPass = true

// 1. 检查核心文件是否存在
console.log('📁 检查核心文件...')
const coreFiles = [
  'src/utils/deliveryTypeConverter.ts',
  'src/pages/cart/index.vue',
  'src/api/takeout.typings.ts',
]

coreFiles.forEach((file) => {
  if (!checkFileExists(file)) {
    allChecksPass = false
  }
})

console.log('')

// 2. 检查转换工具是否正确实现
console.log('🔧 检查配送方式转换工具...')
const converterChecks = [
  ['DeliveryType', '配送方式枚举'],
  ['DELIVERY = 0', '配送订单枚举值'],
  ['PICKUP = 2', '自提订单枚举值'],
  ['convertDeliveryMethodToType', '方式转类型函数'],
  ['convertDeliveryTypeToMethod', '类型转方式函数'],
  ['getDeliveryMethodText', '获取方式文本函数'],
  ['getDeliveryTypeText', '获取类型文本函数'],
  ['isValidDeliveryType', '验证类型有效性函数'],
  ['isValidDeliveryMethod', '验证方式有效性函数'],
]

converterChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/utils/deliveryTypeConverter.ts', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 3. 检查购物车页面是否正确使用deliveryType
console.log('🛒 检查购物车页面配送方式处理...')
const cartChecks = [
  ['deliveryType?: number', 'MerchantOrderRequest接口使用数字类型'],
  ['配送方式：0-配送订单，2-自提订单', '配送方式注释说明'],
  ['deliveryMethod === \'pickup\' ? 2 : 0', '配送方式转换逻辑'],
  ['deliveryTypeText: deliveryType === 2 ? \'自提订单\' : \'配送订单\'', '配送类型文本转换'],
]

cartChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/pages/cart/index.vue', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 4. 检查API类型定义是否更新
console.log('🔌 检查API类型定义...')
const apiChecks = [
  ['deliveryType?: number', 'CreateTakeoutOrderRequest接口包含deliveryType字段'],
  ['配送方式：0-配送订单，2-自提订单', 'deliveryType字段注释说明'],
]

apiChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/api/takeout.typings.ts', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 5. 检查配送方式逻辑是否保持一致
console.log('🚚 检查配送方式逻辑一致性...')
const logicChecks = [
  ['getDeliveryMethod', '获取配送方式函数'],
  ['setDeliveryMethod', '设置配送方式函数'],
  ['deliveryMethod === \'pickup\'', '自提判断逻辑'],
  ['配送费为0', '自提配送费逻辑'],
]

logicChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/pages/cart/index.vue', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 总结
if (allChecksPass) {
  console.log('🎉 所有检查通过！配送方式转换功能实现正确。')
  console.log('')
  console.log('✨ 实现的功能:')
  console.log('  - ✅ 创建了配送方式转换工具 (deliveryTypeConverter.ts)')
  console.log('  - ✅ 更新了MerchantOrderRequest接口使用数字类型deliveryType')
  console.log('  - ✅ 实现了配送方式字符串到数字的转换逻辑')
  console.log('  - ✅ 保持了前端配送方式选择的字符串格式')
  console.log('  - ✅ 更新了API类型定义支持deliveryType字段')
  console.log('  - ✅ 添加了详细的调试信息输出')
  console.log('')
  console.log('🔄 转换规则:')
  console.log('  - delivery (配送) → 0 (配送订单)')
  console.log('  - pickup (自取) → 2 (自提订单)')
  console.log('')
  console.log('🚀 建议下一步:')
  console.log('  1. 在开发环境中测试购物车结算功能')
  console.log('  2. 验证订单创建API是否正确接收deliveryType参数')
  console.log('  3. 检查后端是否正确处理deliveryType=2的自提订单')
  console.log('  4. 测试自提订单的配送费计算是否为0')
  process.exit(0)
} else {
  console.error('❌ 部分检查未通过，请检查上述错误并修复。')
  process.exit(1)
}
