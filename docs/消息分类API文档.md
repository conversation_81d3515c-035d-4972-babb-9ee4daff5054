# 消息分类API文档

## 概述

本文档详细描述了聊天模块中消息分类功能的API接口，支持四类消息分类：

- **聊天消息（chat）**：用户与用户之间的私聊
- **系统通知（system）**：系统级别的通知消息
- **订单消息（order）**：订单相关的状态通知
- **客服消息（service）**：用户与客服、商家的对话

## 基础信息

- **Base URL**: `/api/v1/chat`
- **认证方式**: JWT Token（Header: `Authorization: Bearer <token>`）
- **响应格式**: JSON
- **字符编码**: UTF-8

## API接口列表

### 1. 获取消息分类列表

获取四类消息分类的基本信息和未读统计。

**接口地址**: `GET /api/v1/chat/message-categories`

**请求参数**: 无

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "type": "chat",
      "title": "聊天消息",
      "icon": "chat",
      "color": "#4D8EFF",
      "unread_count": 3,
      "path": "/pages/chat/sessions/index"
    },
    {
      "type": "system",
      "title": "系统通知",
      "icon": "notification",
      "color": "#FF9500",
      "unread_count": 2,
      "path": "/pages/message/system"
    },
    {
      "type": "order",
      "title": "订单消息",
      "icon": "goods",
      "color": "#34C759",
      "unread_count": 4,
      "path": "/pages/message/order"
    },
    {
      "type": "service",
      "title": "客服消息",
      "icon": "service",
      "color": "#FF3B30",
      "unread_count": 1,
      "path": "/pages/message/service"
    }
  ]
}
```

### 2. 获取未读消息统计

获取各类消息的详细未读统计信息。

**接口地址**: `GET /api/v1/chat/unread-count`

**请求参数**: 无

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 10,
    "categories": {
      "chat": 3,
      "system": 2,
      "order": 4,
      "service": 1
    },
    "conversations": {
      "session_123": 2,
      "session_456": 1
    }
  }
}
```

### 3. 获取分类会话列表

根据消息分类获取对应的会话列表。

**接口地址**: `GET /api/v1/chat/sessions`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| category | string | 否 | 消息分类（chat/system/order/service） |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 123,
        "type": "user_to_user",
        "target_name": "张三",
        "target_avatar": "https://example.com/avatar.jpg",
        "last_message": {
          "id": 456,
          "content": "你好",
          "type": "text",
          "created_at": "2025-01-26T10:30:00Z"
        },
        "unread_count": 2,
        "updated_at": "2025-01-26T10:30:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20,
    "page_count": 3,
    "category": "chat"
  }
}
```

### 4. 获取分类消息列表

根据消息分类获取对应的消息列表。

**接口地址**: `GET /api/v1/chat/messages`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 消息分类（chat/system/order/service） |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |
| keyword | string | 否 | 搜索关键词 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 789,
        "title": "订单支付成功",
        "content": "您的订单 #12345 已支付成功",
        "type": "notification",
        "notification_type": "order_paid",
        "status": 0,
        "created_at": "2025-01-26T10:30:00Z",
        "extra_data": {
          "order_id": "12345",
          "amount": "99.00"
        }
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "page_count": 5
  }
}
```

### 5. 标记消息为已读

标记指定消息为已读状态。

**接口地址**: `POST /api/v1/chat/messages/{messageId}/read`

**请求参数**: 无

**响应示例**:

```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 6. 标记分类消息为已读

标记指定分类的所有消息为已读状态。

**接口地址**: `POST /api/v1/chat/categories/{category}/read`

**请求参数**: 无

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| category | string | 是 | 消息分类（chat/system/order/service） |

**响应示例**:

```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 7. 标记会话消息为已读

标记指定会话的所有消息为已读状态。

**接口地址**: `POST /api/v1/chat/sessions/{sessionId}/read`

**请求参数**: 无

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sessionId | int | 是 | 会话ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

## 数据结构说明

### MessageCategoryDTO

```json
{
  "type": "string", // 分类类型
  "title": "string", // 分类标题
  "icon": "string", // 图标名称
  "color": "string", // 主题色
  "unread_count": "int", // 未读数量
  "path": "string" // 页面路径
}
```

### UnreadCountDTO

```json
{
  "total": "int", // 总未读数量
  "categories": {
    // 分类未读统计
    "chat": "int",
    "system": "int",
    "order": "int",
    "service": "int"
  },
  "conversations": {
    // 会话未读统计
    "session_id": "int"
  }
}
```

### SessionDTO

```json
{
  "id": "int", // 会话ID
  "type": "string", // 会话类型
  "target_name": "string", // 对话目标名称
  "target_avatar": "string", // 对话目标头像
  "last_message": "MessageDTO", // 最后一条消息
  "unread_count": "int", // 未读数量
  "created_at": "string", // 创建时间
  "updated_at": "string" // 更新时间
}
```

### MessageListItem

```json
{
  "id": "int", // 消息ID
  "title": "string", // 消息标题
  "content": "string", // 消息内容
  "type": "string", // 消息类型
  "notification_type": "string", // 通知类型
  "status": "int", // 消息状态（0:未读 1:已读）
  "created_at": "string", // 创建时间
  "extra_data": "object" // 扩展数据
}
```

## 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 200    | 成功           |
| 400    | 请求参数错误   |
| 401    | 未授权         |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

## 使用示例

### JavaScript/TypeScript 示例

```javascript
// 获取消息分类列表
async function getMessageCategories() {
  const response = await fetch('/api/v1/chat/message-categories', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
  return await response.json()
}

// 获取聊天会话列表
async function getChatSessions(page = 1) {
  const response = await fetch(`/api/v1/chat/sessions?category=chat&page=${page}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
  return await response.json()
}

// 获取系统通知列表
async function getSystemNotifications(page = 1) {
  const response = await fetch(`/api/v1/chat/messages?type=system&page=${page}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
  return await response.json()
}

// 标记分类为已读
async function markCategoryAsRead(category) {
  const response = await fetch(`/api/v1/chat/categories/${category}/read`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
  return await response.json()
}
```

## 注意事项

1. **认证**: 所有API都需要JWT Token认证
2. **缓存**: 未读消息统计有30秒缓存，消息分类列表有60秒缓存
3. **分页**: 默认每页20条记录，最大支持100条
4. **实时更新**: 建议配合WebSocket使用以获得实时的消息推送
5. **性能**: 高频调用的接口已添加缓存优化
6. **错误处理**: 请根据返回的错误码进行相应的错误处理

## 前端集成最佳实践

### 1. 消息分类页面结构建议

```javascript
// 页面结构示例
const MessageCenterPage = {
  data() {
    return {
      categories: [],
      activeCategory: 'chat',
      messages: [],
      sessions: [],
      loading: false,
    }
  },

  async mounted() {
    await this.loadCategories()
    await this.loadCategoryData(this.activeCategory)
  },

  methods: {
    // 加载消息分类
    async loadCategories() {
      try {
        const result = await this.getMessageCategories()
        this.categories = result.data
      } catch (error) {
        console.error('加载消息分类失败:', error)
      }
    },

    // 切换分类
    async switchCategory(category) {
      this.activeCategory = category
      await this.loadCategoryData(category)
    },

    // 加载分类数据
    async loadCategoryData(category) {
      this.loading = true
      try {
        if (category === 'chat' || category === 'service') {
          // 聊天和客服消息显示会话列表
          const result = await this.getChatSessions(category)
          this.sessions = result.data.list
        } else {
          // 系统通知和订单消息显示消息列表
          const result = await this.getMessages(category)
          this.messages = result.data.list
        }
      } catch (error) {
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
  },
}
```

### 2. 实时更新机制

```javascript
// WebSocket集成示例
class MessageCenterManager {
  constructor() {
    this.ws = null
    this.categories = []
    this.callbacks = new Map()
  }

  // 连接WebSocket
  connect() {
    this.ws = new WebSocket('ws://localhost:8080/ws')

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }
  }

  // 处理WebSocket消息
  handleMessage(data) {
    switch (data.type) {
      case 'new_message':
        this.updateUnreadCount()
        this.notifyCallbacks('message_received', data)
        break
      case 'message_read':
        this.updateUnreadCount()
        this.notifyCallbacks('message_read', data)
        break
    }
  }

  // 更新未读统计
  async updateUnreadCount() {
    try {
      const result = await this.getUnreadCount()
      this.notifyCallbacks('unread_count_updated', result.data)
    } catch (error) {
      console.error('更新未读统计失败:', error)
    }
  }

  // 注册回调
  onEvent(event, callback) {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, [])
    }
    this.callbacks.get(event).push(callback)
  }

  // 通知回调
  notifyCallbacks(event, data) {
    const callbacks = this.callbacks.get(event) || []
    callbacks.forEach((callback) => callback(data))
  }
}
```

### 3. 缓存策略建议

```javascript
// 本地缓存管理
class MessageCache {
  constructor() {
    this.cache = new Map()
    this.expireTime = new Map()
  }

  // 设置缓存
  set(key, data, ttl = 60000) {
    // 默认60秒
    this.cache.set(key, data)
    this.expireTime.set(key, Date.now() + ttl)
  }

  // 获取缓存
  get(key) {
    const expireTime = this.expireTime.get(key)
    if (!expireTime || Date.now() > expireTime) {
      this.cache.delete(key)
      this.expireTime.delete(key)
      return null
    }
    return this.cache.get(key)
  }

  // 清除缓存
  clear(pattern) {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key)
        this.expireTime.delete(key)
      }
    }
  }
}

// 使用示例
const cache = new MessageCache()

async function getMessageCategoriesWithCache() {
  const cacheKey = 'message_categories'
  let data = cache.get(cacheKey)

  if (!data) {
    const result = await getMessageCategories()
    data = result.data
    cache.set(cacheKey, data, 60000) // 缓存60秒
  }

  return data
}
```

## 更新日志

- **v1.0** (2025-01-26): 初始版本，支持四类消息分类的完整功能
