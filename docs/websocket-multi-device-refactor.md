# WebSocket多端登录重构文档

## 概述

本次重构的目标是为WebSocket连接添加多端登录支持，通过在连接URL中添加`device_id`参数来实现多设备同时在线的功能。

## 重构内容

### 1. WebSocket服务层重构 (`src/service/websocket.ts`)

#### 修改内容：

- **connect方法签名更新**：添加可选的`deviceId`参数

  ```typescript
  // 修改前
  public connect(token: string): Promise<void>

  // 修改后
  public connect(token: string, deviceId?: string): Promise<void>
  ```

- **URL构建逻辑优化**：

  ```typescript
  // 获取设备ID - 优先使用传入的deviceId，否则从存储中获取
  const currentDeviceId =
    deviceId || uni.getStorageSync('device_id') || uni.getStorageSync('current_device_id')

  // 构建查询参数
  const params = new URLSearchParams()
  params.append('token', latestToken)
  if (currentDeviceId) {
    params.append('device_id', currentDeviceId)
  }
  const fullUrl = `${wsUrl}?${params.toString()}`
  ```

#### 特性：

- 向后兼容：`deviceId`参数为可选，不传入时自动从本地存储获取
- 自动获取：支持从`device_id`或`current_device_id`存储键获取设备ID
- 参数化URL：使用`URLSearchParams`构建查询参数，确保URL格式正确

### 2. WebSocket Store重构 (`src/store/websocket.ts`)

#### 修改内容：

- **连接逻辑更新**：在`connectWebSocket`方法中添加设备ID获取和传递

  ```typescript
  // 获取设备ID
  const deviceId = uni.getStorageSync('device_id') || uni.getStorageSync('current_device_id')

  // 连接WebSocket，传递token和device_id
  websocketService.connect(userStore.userInfo.token, deviceId)
  ```

#### 特性：

- 自动获取设备ID并传递给WebSocket服务
- 增强日志记录，显示连接时使用的设备ID

### 3. 聊天室页面重构 (`src/pages/chat/room/index.vue`)

#### 修改内容：

- **初始化逻辑更新**：在`initWebSocket`函数中添加设备ID获取和传递

  ```typescript
  // 获取设备ID
  const deviceId = uni.getStorageSync('device_id') || uni.getStorageSync('current_device_id')

  // 创建WebSocket连接
  websocketService.connect(token, deviceId)
  ```

#### 特性：

- 页面级别的WebSocket连接也支持多端登录
- 增强日志记录，便于调试

## 设备信息管理

### 设备ID生成和存储

项目中已有完善的设备信息管理机制：

1. **设备信息生成** (`src/utils/device.ts`)：

   - `generateDeviceInfo()`：生成完整的设备信息
   - `getOrCreateDeviceId()`：获取或创建设备唯一标识

2. **存储机制**：

   - `device_id`：本地生成的设备唯一标识
   - `current_device_id`：服务器返回的设备ID（登录时保存）

3. **用户登录集成** (`src/store/user.ts`)：
   - 登录时自动生成并发送设备信息
   - 保存服务器返回的设备ID

## 连接URL格式

### 重构前：

```
ws://localhost:8080/api/v1/chat/ws?token=your_jwt_token
```

### 重构后：

```
ws://localhost:8080/api/v1/chat/ws?token=your_jwt_token&device_id=unique_device_identifier
```

## 测试工具

### 1. WebSocket测试工具 (`src/utils/websocket-test.ts`)

提供以下测试功能：

- `testMultiDeviceWebSocketConnection`：测试单设备连接
- `validateWebSocketURL`：验证URL参数构建
- `testMultipleDeviceConnections`：模拟多设备同时连接

### 2. 测试页面 (`src/pages/test/websocket.vue`)

提供可视化测试界面：

- 显示当前设备信息
- WebSocket连接状态监控
- 单设备和多设备连接测试
- URL参数验证
- 实时日志显示

## 使用方法

### 基本连接

```typescript
import { WebSocketService } from '@/service/websocket'

const wsService = new WebSocketService()
const token = 'your_jwt_token'
const deviceId = 'unique_device_identifier'

// 连接WebSocket
await wsService.connect(token, deviceId)
```

### 通过Store连接

```typescript
import { useWebSocketStore } from '@/store/websocket'

const wsStore = useWebSocketStore()
// 自动获取设备ID并连接
wsStore.connectWebSocket()
```

## 兼容性说明

- **向后兼容**：所有现有代码无需修改即可正常工作
- **可选参数**：`deviceId`参数为可选，不传入时自动获取
- **自动降级**：如果无法获取设备ID，连接仍然可以建立（仅使用token）

## 注意事项

1. **设备ID获取优先级**：

   - 传入的`deviceId`参数（最高优先级）
   - `device_id`存储键
   - `current_device_id`存储键

2. **错误处理**：

   - 如果设备ID获取失败，连接仍会尝试建立
   - 所有错误都会被适当记录和处理

3. **性能考虑**：
   - 设备ID获取是同步操作，不会影响连接性能
   - URL构建使用标准`URLSearchParams`，确保正确编码

## 后续优化建议

1. **设备管理界面**：可以考虑添加设备管理页面，显示当前登录的所有设备
2. **设备踢出功能**：支持从其他设备踢出当前设备
3. **设备信任机制**：标记信任设备，减少安全验证
4. **连接状态同步**：在多设备间同步连接状态和消息

## 测试验证

使用提供的测试工具可以验证：

- ✅ WebSocket连接URL正确包含`device_id`参数
- ✅ 多设备可以同时建立连接
- ✅ 设备ID正确传递到服务器
- ✅ 向后兼容性保持良好

## 总结

本次重构成功为WebSocket连接添加了多端登录支持，同时保持了良好的向后兼容性。通过合理的参数设计和自动获取机制，使得现有代码无需修改即可享受多端登录的功能。
