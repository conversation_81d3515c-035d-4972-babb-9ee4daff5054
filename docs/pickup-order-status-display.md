# 自取订单状态显示优化文档

## 📋 需求概述

当订单为自取订单（`delivery_type = 2`）且处于"取货中"状态（`delivery_status = 30`）时，订单列表页面应该显示"等待自取"而不是原来的"处理中"状态。

## 🎯 具体需求

根据订单数据：
```json
{
  "id": 102,
  "order_no": "202507312242578223",
  "status": 30,
  "status_text": "处理中",
  "delivery_status": 30,
  "delivery_status_text": "取货中",
  "delivery_type": 2,
  "delivery_type_text": "到店自取"
}
```

当满足以下条件时：
- `delivery_type = 2` (到店自取)
- `status = 30` (处理中)
- `delivery_status = 30` (取货中)

订单状态应显示为"等待自取"而不是"处理中"。

## 🔧 实现方案

### 1. 创建状态显示处理函数

在 `src/pages/order/list.vue` 中添加新的状态显示函数：

```typescript
/**
 * 获取显示状态文本（处理自取订单的特殊状态）
 */
const getDisplayStatusText = (order: any) => {
  // 如果是自取订单（delivery_type = 2）且订单状态为处理中（status = 30）且取货状态为取货中（delivery_status = 30）
  if (order.delivery_type === 2 && order.status === 30 && order.delivery_status === 30) {
    console.log(`🏪 订单${order.id}为自取订单，显示"等待自取"状态`, {
      orderId: order.id,
      orderNo: order.order_no,
      deliveryType: order.delivery_type,
      deliveryTypeText: order.delivery_type_text,
      status: order.status,
      statusText: order.status_text,
      deliveryStatus: order.delivery_status,
      deliveryStatusText: order.delivery_status_text,
      displayText: '等待自取'
    })
    return '等待自取'
  }
  
  // 其他情况使用原有逻辑
  const displayText = order.status_text || getStatusText(order.status)
  
  // 如果是自取订单，记录调试信息
  if (order.delivery_type === 2) {
    console.log(`🏪 订单${order.id}为自取订单，但不满足"等待自取"条件`, {
      orderId: order.id,
      orderNo: order.order_no,
      deliveryType: order.delivery_type,
      deliveryTypeText: order.delivery_type_text,
      status: order.status,
      statusText: order.status_text,
      deliveryStatus: order.delivery_status,
      deliveryStatusText: order.delivery_status_text,
      displayText: displayText
    })
  }
  
  return displayText
}
```

### 2. 更新模板调用

将原来的状态显示：
```vue
<view class="order-status" :class="getStatusClass((order as any).status)">
  {{ (order as any).status_text || getStatusText((order as any).status) }}
</view>
```

修改为：
```vue
<view class="order-status" :class="getStatusClass((order as any).status)">
  {{ getDisplayStatusText(order as any) }}
</view>
```

## 🔄 状态显示逻辑

### 判断条件

| 字段 | 值 | 说明 |
|------|----|----|
| `delivery_type` | `2` | 到店自取 |
| `status` | `30` | 处理中 |
| `delivery_status` | `30` | 取货中 |

### 显示规则

1. **满足特殊条件**：显示"等待自取"
   - 自取订单 + 订单状态30(处理中) + 取货状态30(取货中) → "等待自取"

2. **其他所有情况**：使用原有状态文本
   - 配送订单：显示原有状态文本
   - 自取订单但不满足条件：显示原有状态文本

## 🧪 测试用例

### 测试数据

| 订单类型 | status | delivery_status | delivery_type | 期望显示 | 说明 |
|----------|--------|-----------------|---------------|----------|------|
| 自取订单 | 30 | 30 | 2 | "等待自取" | ✅ 特殊处理 |
| 配送订单 | 30 | 30 | 0 | "处理中" | ✅ 原有逻辑 |
| 自取订单 | 20 | 10 | 2 | "待发货" | ✅ 原有逻辑 |
| 自取订单 | 50 | 50 | 2 | "已完成" | ✅ 原有逻辑 |

### 验证脚本

创建了自动化测试脚本 `test-pickup-order-status.js`：
- ✅ 检查核心文件存在性
- ✅ 验证状态显示函数实现
- ✅ 检查模板更新
- ✅ 模拟数据逻辑验证

## 🎯 关键特性

### 1. 精确匹配
- 只有同时满足三个条件才显示"等待自取"
- 避免误判其他状态的订单

### 2. 向后兼容
- 不影响配送订单的状态显示
- 不影响自取订单的其他状态显示
- 保持原有的状态样式类

### 3. 调试友好
- 详细的控制台输出
- 记录所有自取订单的状态判断过程
- 便于开发和调试

### 4. 类型安全
- 使用TypeScript类型检查
- 保持原有函数签名兼容性

## 📁 修改文件清单

### 主要修改
- ✅ `src/pages/order/list.vue` - 添加状态显示处理函数和更新模板

### 新增文件
- ✅ `test-pickup-order-status.js` - 自动化测试脚本
- ✅ `docs/pickup-order-status-display.md` - 本文档

## 🎉 实现效果

### 显示效果对比

**修改前：**
```
自取订单 - 取货中状态：显示 "处理中"
```

**修改后：**
```
自取订单 - 取货中状态：显示 "等待自取"
```

### 调试输出示例

```javascript
🏪 订单102为自取订单，显示"等待自取"状态 {
  orderId: 102,
  orderNo: "202507312242578223",
  deliveryType: 2,
  deliveryTypeText: "到店自取",
  status: 30,
  statusText: "处理中",
  deliveryStatus: 30,
  deliveryStatusText: "取货中",
  displayText: "等待自取"
}
```

## 🚀 后续建议

1. **用户体验测试**：在实际环境中测试用户对"等待自取"状态的理解
2. **状态流转验证**：确认自取订单的完整状态流转是否正确
3. **通知优化**：考虑为"等待自取"状态添加特殊的通知提醒
4. **UI优化**：考虑为"等待自取"状态添加特殊的图标或颜色

现在订单列表页面已经能够正确识别自取订单的"取货中"状态，并显示更加用户友好的"等待自取"文本！
