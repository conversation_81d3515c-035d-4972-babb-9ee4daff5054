# 配送方式字段适配文档

## 📋 需求概述

用户要求修改创建自提订单时的`MerchantOrderRequest`中的`deliveryType`字段，使其使用数字格式：
- `0` = 配送订单
- `2` = 自提订单

## 🔧 实现方案

### 1. 接口定义更新

#### 1.1 MerchantOrderRequest接口 (`src/pages/cart/index.vue`)

**修改前：**
```typescript
interface MerchantOrderRequest {
  merchantID: number
  cartItemIDs: number[]
  couponID?: number
  promotionID?: number
  deliveryTime?: string
  remark?: string
  deliveryType?: 'delivery' | 'pickup' // 配送方式
}
```

**修改后：**
```typescript
interface MerchantOrderRequest {
  merchantID: number
  cartItemIDs: number[]
  couponID?: number
  promotionID?: number
  deliveryTime?: string
  remark?: string
  deliveryType?: number // 配送方式：0-配送订单，2-自提订单
}
```

#### 1.2 CreateTakeoutOrderRequest接口 (`src/api/takeout.typings.ts`)

**新增字段：**
```typescript
export interface CreateTakeoutOrderRequest {
  takeoutAddressID: number
  deliveryTime?: string
  remark?: string
  paymentMethod: string
  couponID?: number
  cartItemIDs: number[]
  deliveryType?: number // 配送方式：0-配送订单，2-自提订单
}
```

### 2. 转换逻辑实现

#### 2.1 订单创建时的转换 (`src/pages/cart/index.vue`)

**修改前：**
```typescript
const merchantOrder: MerchantOrderRequest = {
  merchantID: group.merchantId,
  cartItemIDs: cartItemIDs,
  remark: group.remark || '',
  deliveryMethod: getDeliveryMethod(group.merchantId),
}
```

**修改后：**
```typescript
const deliveryMethod = getDeliveryMethod(group.merchantId)
const deliveryType = deliveryMethod === 'pickup' ? 2 : 0
const merchantOrder: MerchantOrderRequest = {
  merchantID: group.merchantId,
  cartItemIDs: cartItemIDs,
  remark: group.remark || '',
  deliveryType: deliveryType, // 配送方式：0-配送订单，2-自提订单
}
```

#### 2.2 调试信息增强

**新增调试输出：**
```typescript
console.log(`📦 商家 ${group.merchantId} 订单配送信息:`, {
  deliveryMethod: deliveryMethod,
  deliveryType: deliveryType,
  deliveryTypeText: deliveryType === 2 ? '自提订单' : '配送订单',
  merchantName: group.merchantName,
  supportPickup: group.supportPickup,
})
```

### 3. 转换工具创建

#### 3.1 配送方式转换工具 (`src/utils/deliveryTypeConverter.ts`)

**新增工具文件：**
```typescript
export enum DeliveryType {
  DELIVERY = 0, // 配送订单
  PICKUP = 2,   // 自提订单
}

export type DeliveryMethod = 'delivery' | 'pickup'

// 转换函数
export function convertDeliveryMethodToType(method: DeliveryMethod): number
export function convertDeliveryTypeToMethod(type: number): DeliveryMethod
export function getDeliveryMethodText(method: DeliveryMethod): string
export function getDeliveryTypeText(type: number): string
export function isValidDeliveryType(type: number): boolean
export function isValidDeliveryMethod(method: string): method is DeliveryMethod
```

## 🔄 数据流程

### 1. 前端状态管理
```
用户选择配送方式 → deliveryMethods.value[merchantId] = 'pickup'/'delivery'
```

### 2. 订单创建转换
```
getDeliveryMethod(merchantId) → 'pickup'/'delivery'
↓
deliveryMethod === 'pickup' ? 2 : 0
↓
deliveryType: number (0 或 2)
```

### 3. API调用
```
MerchantOrderRequest { deliveryType: 0|2 } → 后端API
```

## 🎯 关键特性

### 1. 向后兼容
- 前端配送方式选择仍使用字符串格式 (`'delivery'` | `'pickup'`)
- 只在创建订单时转换为数字格式
- 不影响现有的配送费计算逻辑

### 2. 类型安全
- 使用TypeScript枚举定义配送类型
- 提供类型检查和转换函数
- 完整的类型定义和注释

### 3. 调试友好
- 详细的控制台输出
- 包含配送方式和类型的对比信息
- 便于开发和调试

## 🧪 测试验证

### 1. 自动化测试
创建了测试脚本 `test-delivery-type-conversion.js` 验证：
- ✅ 核心文件存在性
- ✅ 转换工具实现完整性
- ✅ 购物车页面配送方式处理
- ✅ API类型定义更新
- ✅ 配送方式逻辑一致性

### 2. 功能测试建议
1. **配送订单测试**：选择配送方式，验证 `deliveryType = 0`
2. **自提订单测试**：选择自取方式，验证 `deliveryType = 2`
3. **配送费计算**：验证自提订单配送费为0
4. **API集成**：验证后端正确接收和处理数字格式

## 📁 修改文件清单

### 主要修改
- ✅ `src/pages/cart/index.vue` - 更新接口定义和转换逻辑
- ✅ `src/api/takeout.typings.ts` - 添加deliveryType字段

### 新增文件
- ✅ `src/utils/deliveryTypeConverter.ts` - 配送方式转换工具
- ✅ `test-delivery-type-conversion.js` - 自动化测试脚本
- ✅ `docs/delivery-type-field-adaptation.md` - 本文档

## 🎉 实现效果

### 转换规则
- `'delivery'` (外卖配送) → `0` (配送订单)
- `'pickup'` (到店自取) → `2` (自提订单)

### 调试输出示例
```
📦 商家 1001 订单配送信息: {
  deliveryMethod: "pickup",
  deliveryType: 2,
  deliveryTypeText: "自提订单",
  merchantName: "美味餐厅",
  supportPickup: 1
}
```

## 🚀 后续建议

1. **后端验证**：确认后端API正确处理 `deliveryType` 数字字段
2. **业务逻辑**：验证自提订单的业务流程（如通知、状态更新等）
3. **用户体验**：测试自提订单的完整用户流程
4. **文档更新**：更新API文档说明 `deliveryType` 字段格式

现在系统已经成功适配了 `deliveryType` 数字字段，支持创建配送订单（0）和自提订单（2），同时保持了前端配送方式选择的用户体验！
