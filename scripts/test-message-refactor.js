#!/usr/bin/env node

/**
 * 消息页面重构验证脚本
 * 检查重构后的代码是否符合API文档要求
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证消息页面重构...\n')

// 检查文件是否存在
const checkFileExists = (filePath) => {
  const fullPath = path.join(__dirname, '..', filePath)
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${filePath}`)
    return false
  }
  console.log(`✅ 文件存在: ${filePath}`)
  return true
}

// 检查文件内容是否包含指定字符串
const checkFileContains = (filePath, searchStrings, description) => {
  const fullPath = path.join(__dirname, '..', filePath)
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ 文件不存在: ${filePath}`)
    return false
  }

  const content = fs.readFileSync(fullPath, 'utf8')
  const results = searchStrings.map((str) => ({
    string: str,
    found: content.includes(str),
  }))

  const allFound = results.every((r) => r.found)
  if (allFound) {
    console.log(`✅ ${description}: ${filePath}`)
  } else {
    console.error(`❌ ${description}: ${filePath}`)
    results
      .filter((r) => !r.found)
      .forEach((r) => {
        console.error(`   缺少: ${r.string}`)
      })
  }
  return allFound
}

let allChecksPass = true

// 1. 检查核心文件是否存在
console.log('📁 检查核心文件...')
const coreFiles = [
  'src/store/message.ts',
  'src/pages/message/index.vue',
  'src/pages/message/system.vue',
  'src/pages/message/order.vue',
  'src/pages/message/service.vue',
  'src/api/message.ts',
]

coreFiles.forEach((file) => {
  if (!checkFileExists(file)) {
    allChecksPass = false
  }
})

console.log('')

// 2. 检查 store 是否正确实现
console.log('🏪 检查消息 Store 实现...')
const storeChecks = [
  ['useMessageStore', 'Store 导出'],
  ['messageCategories', '消息分类状态'],
  ['systemNotifications', '系统通知状态'],
  ['orderNotifications', '订单通知状态'],
  ['serviceMessages', '客服消息状态'],
  ['fetchMessageCategories', '获取消息分类方法'],
  ['fetchSystemNotifications', '获取系统通知方法'],
  ['fetchOrderNotifications', '获取订单通知方法'],
  ['fetchServiceMessages', '获取客服消息方法'],
  ['unread_count', '字段名转换处理'],
  ['unreadCount', '转换后的字段名'],
]

storeChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/store/message.ts', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 3. 检查 API 接口是否更新
console.log('🔌 检查 API 接口更新...')
const apiChecks = [
  ['/messages', '消息列表接口路径'],
  ["type: 'system'", '系统通知类型参数'],
  ["type: 'order'", '订单通知类型参数'],
  ["category: 'service'", '客服消息分类参数'],
  ['/sessions', '客服会话接口路径'],
]

apiChecks.forEach(([searchString, description]) => {
  if (!checkFileContains('src/api/message.ts', [searchString], description)) {
    allChecksPass = false
  }
})

console.log('')

// 4. 检查页面组件是否使用新的 store
console.log('📄 检查页面组件重构...')
const pageChecks = [
  {
    file: 'src/pages/message/index.vue',
    checks: [
      ['useMessageStore', '使用消息 Store'],
      ['messageStore.messageCategories', '使用分类状态'],
      ['messageStore.initMessageData', '初始化数据方法'],
    ],
  },
  {
    file: 'src/pages/message/system.vue',
    checks: [
      ['useMessageStore', '使用消息 Store'],
      ['messageStore.systemNotifications', '使用系统通知状态'],
      ['messageStore.fetchSystemNotifications', '获取系统通知方法'],
    ],
  },
  {
    file: 'src/pages/message/order.vue',
    checks: [
      ['useMessageStore', '使用消息 Store'],
      ['messageStore.orderNotifications', '使用订单通知状态'],
      ['messageStore.fetchOrderNotifications', '获取订单通知方法'],
    ],
  },
  {
    file: 'src/pages/message/service.vue',
    checks: [
      ['useMessageStore', '使用消息 Store'],
      ['messageStore.serviceMessages', '使用客服消息状态'],
      ['messageStore.fetchServiceMessages', '获取客服消息方法'],
    ],
  },
]

pageChecks.forEach(({ file, checks }) => {
  console.log(`  检查 ${file}:`)
  checks.forEach(([searchString, description]) => {
    if (!checkFileContains(file, [searchString], `    ${description}`)) {
      allChecksPass = false
    }
  })
})

console.log('')

// 5. 检查 store 是否正确导出
console.log('📤 检查 Store 导出...')
if (!checkFileContains('src/store/index.ts', ["export * from './message'"], 'Store 导出')) {
  allChecksPass = false
}

console.log('')

// 总结
if (allChecksPass) {
  console.log('🎉 所有检查通过！消息页面重构成功完成。')
  console.log('')
  console.log('✨ 重构完成的功能:')
  console.log('  - ✅ 创建了专门的消息状态管理 Store')
  console.log('  - ✅ 更新了 API 接口调用以符合文档规范')
  console.log('  - ✅ 重构了消息主页面使用新的 Store')
  console.log('  - ✅ 重构了系统通知页面')
  console.log('  - ✅ 重构了订单消息页面')
  console.log('  - ✅ 重构了客服消息页面')
  console.log('  - ✅ 处理了 API 字段名差异 (snake_case ↔ camelCase)')
  console.log('  - ✅ 实现了统一的分页和加载状态管理')
  console.log('')
  console.log('🚀 建议下一步:')
  console.log('  1. 运行单元测试验证功能正确性')
  console.log('  2. 在开发环境中测试页面交互')
  console.log('  3. 验证 API 调用是否正确')
  process.exit(0)
} else {
  console.error('❌ 部分检查未通过，请检查上述错误并修复。')
  process.exit(1)
}
