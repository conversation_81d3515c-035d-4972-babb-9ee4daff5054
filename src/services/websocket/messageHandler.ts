import type {
  WebSocketMessage,
  NotificationData,
  ChatMessageData,
  SystemMessageData,
  OrderMessageData,
  BalanceChangeData,
  CouponMessageData,
  PointsMessageData,
} from './types'

/**
 * WebSocket消息处理中心
 * 集中处理所有用户相关的WebSocket消息
 */
export class WebSocketMessageHandler {
  private static instance: WebSocketMessageHandler | null = null

  private constructor() {
    console.log('📨 WebSocket消息处理中心初始化')
  }

  public static getInstance(): WebSocketMessageHandler {
    if (!this.instance) {
      this.instance = new WebSocketMessageHandler()
    }
    return this.instance
  }

  /**
   * 处理WebSocket消息的统一入口
   */
  public handleMessage(message: any): void {
    console.log('📨 处理WebSocket消息:', message)

    try {
      // 只处理用户相关消息，忽略商家和管理员消息
      if (!this.isUserMessage(message)) {
        // 减少控制台噪音，只在开发环境输出详细日志
        if (import.meta.env.DEV) {
          console.debug('🚫 忽略非用户消息:', message.event)
        }
        return
      }

      switch (message.type) {
        case 'notification':
          this.handleNotification(message)
          break
        case 'message':
          this.handleChatMessage(message)
          break
        case 'system':
          this.handleSystemMessage(message)
          break
        case 'heartbeat':
          this.handleHeartbeat(message)
          break
        default:
          console.warn('⚠️ 未知消息类型:', message.type)
      }
    } catch (error) {
      console.error('❌ 处理WebSocket消息时出错:', error)
    }
  }

  /**
   * 判断是否为用户消息
   */
  private isUserMessage(message: any): boolean {
    const event = message.event || ''
    const type = message.type || ''

    // 通用消息类型 - 用户端需要处理
    const commonMessageEvents = [
      'text_message', // 文本消息
      'media_message', // 媒体消息
      'system_message', // 系统消息
    ]

    // 用户相关事件前缀
    const userEventPrefixes = [
      'user_', // 用户通知
      'runner_', // 跑腿员通知（跑腿员也是用户）
    ]

    // 商家和管理员事件前缀 - 用户端应该忽略
    const ignoredEventPrefixes = [
      'merchant_', // 商家消息
      'system_maintenance', // 系统维护
      'system_announcement', // 系统公告
      'system_error_alert', // 系统错误告警
      'performance_alert', // 性能告警
      'security_alert', // 安全告警
      'order_exception', // 订单异常
      'payment_exception', // 支付异常
      'refund_exception', // 退款异常
      'user_report', // 用户举报（管理员处理）
      'user_ban', // 用户封禁（管理员处理）
      'merchant_audit', // 商家审核
      'merchant_violation', // 商家违规
      'daily_report', // 日报
      'threshold_alert', // 阈值告警
    ]

    // 检查是否为需要忽略的事件
    for (const prefix of ignoredEventPrefixes) {
      if (event.startsWith(prefix) || event === prefix) {
        return false
      }
    }

    // 检查是否为通用消息
    if (commonMessageEvents.includes(event)) {
      return true
    }

    // 检查是否为用户相关事件
    for (const prefix of userEventPrefixes) {
      if (event.startsWith(prefix)) {
        return true
      }
    }

    // 默认情况下，如果不确定，记录日志并返回false
    if (import.meta.env.DEV) {
      console.debug('⚠️ 未知事件类型，默认忽略:', event, '消息类型:', type)
    }
    return false
  }

  /**
   * 处理通知消息
   */
  private handleNotification(message: WebSocketMessage): void {
    const { event, data } = message

    // 导入通知服务（延迟导入避免循环依赖）
    import('./notificationService').then(({ NotificationService }) => {
      const notificationService = NotificationService.getInstance()
      notificationService.showNotification(data)
    })

    // 处理具体业务逻辑
    switch (event) {
      // 订单相关通知
      case 'user_order_payment_success':
        this.handleOrderPaymentSuccess(data)
        break
      case 'user_order_status_update':
        this.handleOrderStatusUpdate(data)
        break
      case 'user_order_delivery_update':
        this.handleOrderDeliveryUpdate(data)
        break
      case 'user_order_completed':
        this.handleOrderCompleted(data)
        break
      case 'user_order_cancelled':
        this.handleOrderCancelled(data)
        break

      // 退款相关通知
      case 'user_refund_approved':
      case 'user_refund_rejected':
      case 'user_refund_processed':
        this.handleRefundUpdate(data)
        break

      // 优惠券相关通知
      case 'user_coupon_received':
        this.handleCouponReceived(data)
        break
      case 'user_coupon_expire_reminder':
        this.handleCouponExpireReminder(data)
        break

      // 积分相关通知
      case 'user_points_earned':
      case 'user_points_redeemed':
        this.handlePointsUpdate(data)
        break

      // 账户相关通知
      case 'user_balance_change':
        this.handleBalanceChange(data)
        break
      case 'user_account_security':
        this.handleAccountSecurity(data)
        break

      // 活动相关通知
      case 'user_promotion_available':
        this.handlePromotionAvailable(data)
        break

      // 评价相关通知
      case 'user_review_reminder':
        this.handleReviewReminder(data)
        break

      // 收藏和关注通知
      case 'user_favorite_price_drop':
        this.handleFavoritePriceDrop(data)
        break
      case 'user_followed_merchant_update':
        this.handleFollowedMerchantUpdate(data)
        break

      // 跑腿员任务相关通知
      case 'runner_task_assigned':
        this.handleRunnerTaskAssigned(data)
        break
      case 'runner_task_status_update':
        this.handleRunnerTaskStatusUpdate(data)
        break
      case 'runner_task_cancelled':
        this.handleRunnerTaskCancelled(data)
        break

      // 跑腿员收益相关通知
      case 'runner_earnings':
        this.handleRunnerEarnings(data)
        break
      case 'runner_withdrawal':
        this.handleRunnerWithdrawal(data)
        break
      case 'runner_daily_earnings':
        this.handleRunnerDailyEarnings(data)
        break

      // 跑腿员状态相关通知
      case 'runner_status_change':
        this.handleRunnerStatusChange(data)
        break
      case 'runner_location_update':
        this.handleRunnerLocationUpdate(data)
        break

      default:
        console.log('📝 未处理的通知事件:', event)
    }
  }

  /**
   * 处理聊天消息
   */
  private handleChatMessage(message: WebSocketMessage): void {
    const { event, data } = message

    console.log('💬 处理聊天消息:', event, data)

    // 根据事件类型处理不同的消息
    switch (event) {
      case 'text_message':
        this.handleTextMessage(data as ChatMessageData)
        break
      case 'media_message':
        this.handleMediaMessage(data as ChatMessageData)
        break
      case 'system_message':
        this.handleChatSystemMessage(data)
        break
      default:
        console.warn('⚠️ 未知聊天消息事件:', event)
        // 兜底处理：按照通用聊天消息处理
        this.handleTextMessage(data as ChatMessageData)
    }
  }

  /**
   * 处理文本消息
   */
  private handleTextMessage(data: ChatMessageData): void {
    console.log('📝 处理文本消息:', data)

    // 更新聊天store
    this.updateChatStore(data)

    // 如果不在聊天页面，显示新消息通知
    this.showChatNotificationIfNeeded(data)
  }

  /**
   * 处理媒体消息（图片、文件、语音、视频）
   */
  private handleMediaMessage(data: ChatMessageData): void {
    console.log('🖼️ 处理媒体消息:', data)

    // 更新聊天store
    this.updateChatStore(data)

    // 如果不在聊天页面，显示新消息通知
    this.showChatNotificationIfNeeded(data)
  }

  /**
   * 处理聊天中的系统消息
   */
  private handleChatSystemMessage(data: any): void {
    console.log('🔔 处理聊天系统消息:', data)

    // 显示系统通知
    import('./notificationService').then(({ NotificationService }) => {
      const notificationService = NotificationService.getInstance()
      notificationService.showNotification(data)
    })
  }

  /**
   * 如果不在聊天页面，显示新消息通知
   */
  private showChatNotificationIfNeeded(data: ChatMessageData): void {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage?.route || ''

      console.log('📍 当前页面路由:', currentRoute)

      // 检查是否在聊天页面（更宽松的检查）
      const isInChatPage = currentRoute.includes('chat') || currentRoute.includes('message')

      console.log('💬 是否在聊天页面:', isInChatPage)

      if (!isInChatPage) {
        console.log('🔔 显示聊天通知...')
        import('./notificationService')
          .then(({ NotificationService }) => {
            const notificationService = NotificationService.getInstance()
            notificationService.showChatNotification(data)
          })
          .catch((error) => {
            console.error('❌ 导入通知服务失败:', error)
            // 降级处理：直接使用uni.showToast
            this.showFallbackNotification(data)
          })
      } else {
        console.log('📱 当前在聊天页面，不显示通知')
      }
    } catch (error) {
      console.error('❌ 检查页面路由失败:', error)
      // 出错时也显示通知
      this.showFallbackNotification(data)
    }
  }

  /**
   * 降级通知显示（当WOT UI不可用时）
   */
  private showFallbackNotification(data: ChatMessageData): void {
    const message = `${data.sender_name || '新消息'}: ${this.truncateMessage(data.content || '', 20)}`

    uni.showToast({
      title: message,
      icon: 'none',
      duration: 3000,
    })
  }

  /**
   * 截断消息内容
   */
  private truncateMessage(message: string, maxLength: number): string {
    if (message.length <= maxLength) {
      return message
    }
    return message.substring(0, maxLength) + '...'
  }

  /**
   * 处理系统消息
   */
  private handleSystemMessage(message: WebSocketMessage): void {
    const { event, data } = message as { event: string; data: SystemMessageData }

    switch (event) {
      case 'user_system_maintenance':
        this.handleSystemMaintenance(data)
        break
      case 'user_version_update':
        this.handleVersionUpdate(data)
        break
      case 'user_service_announcement':
        this.handleServiceAnnouncement(data)
        break
      default:
        console.log('📝 未处理的系统消息:', event)
    }
  }

  /**
   * 处理心跳消息
   */
  private handleHeartbeat(message: WebSocketMessage): void {
    console.log('💓 收到心跳响应')
  }

  /**
   * 连接建立后的处理
   */
  public onConnectionEstablished(): void {
    console.log('🎉 WebSocket连接已建立，开始接收消息')

    // 可以在这里发送一些初始化消息
    // 比如获取离线消息等
    this.requestOfflineMessages()
  }

  // ==================== 具体业务处理方法 ====================

  /**
   * 处理订单支付成功
   */
  private handleOrderPaymentSuccess(data: OrderMessageData): void {
    console.log('💰 订单支付成功:', data)

    // 更新订单状态
    this.updateOrderStore(data.order_id, 'paid')

    // 可以触发一些UI更新或跳转
    if (data.order_id) {
      // 可以选择跳转到订单详情页
      // uni.navigateTo({ url: `/pages/order/detail?id=${data.order_id}` })
    }
  }

  /**
   * 处理订单状态更新
   */
  private handleOrderStatusUpdate(data: OrderMessageData): void {
    console.log('📦 订单状态更新:', data)

    if (data.order_id && data.status) {
      this.updateOrderStore(data.order_id, data.status)
    }
  }

  /**
   * 处理订单配送更新
   */
  private handleOrderDeliveryUpdate(data: OrderMessageData): void {
    console.log('🚚 订单配送更新:', data)

    if (data.order_id) {
      this.updateOrderStore(data.order_id, 'shipping')
    }
  }

  /**
   * 处理订单完成
   */
  private handleOrderCompleted(data: OrderMessageData): void {
    console.log('✅ 订单已完成:', data)

    if (data.order_id) {
      this.updateOrderStore(data.order_id, 'completed')
    }
  }

  /**
   * 处理订单取消
   */
  private handleOrderCancelled(data: OrderMessageData): void {
    console.log('❌ 订单已取消:', data)

    if (data.order_id) {
      this.updateOrderStore(data.order_id, 'cancelled')
    }
  }

  /**
   * 处理退款更新
   */
  private handleRefundUpdate(data: any): void {
    console.log('💸 退款状态更新:', data)

    // 更新退款相关状态
    // 可以调用退款store的更新方法
  }

  /**
   * 处理优惠券接收
   */
  private handleCouponReceived(data: CouponMessageData): void {
    console.log('🎫 收到优惠券:', data)

    // 更新优惠券store
    this.updateCouponStore(data.coupon)
  }

  /**
   * 处理优惠券过期提醒
   */
  private handleCouponExpireReminder(data: CouponMessageData): void {
    console.log('⏰ 优惠券即将过期:', data)
  }

  /**
   * 处理积分更新
   */
  private handlePointsUpdate(data: PointsMessageData): void {
    console.log('⭐ 积分更新:', data)

    // 更新用户积分
    this.updateUserPoints(data.points)
  }

  /**
   * 处理余额变动
   */
  private handleBalanceChange(data: BalanceChangeData): void {
    console.log('💰 余额变动:', data)

    // 更新用户余额
    this.updateUserBalance(data.balance)
  }

  /**
   * 处理账户安全通知
   */
  private handleAccountSecurity(data: any): void {
    console.log('🔒 账户安全通知:', data)

    if (data.security_type === 'login_alert') {
      // 异地登录警告
      import('./notificationService').then(({ NotificationService }) => {
        const notificationService = NotificationService.getInstance()
        notificationService.showSecurityAlert(data)
      })
    }
  }

  /**
   * 处理促销活动通知
   */
  private handlePromotionAvailable(data: any): void {
    console.log('🎉 促销活动通知:', data)
  }

  /**
   * 处理评价提醒
   */
  private handleReviewReminder(data: any): void {
    console.log('⭐ 评价提醒:', data)
  }

  /**
   * 处理收藏商品降价通知
   */
  private handleFavoritePriceDrop(data: any): void {
    console.log('💰 收藏商品降价:', data)
  }

  /**
   * 处理关注商家更新
   */
  private handleFollowedMerchantUpdate(data: any): void {
    console.log('🏪 关注商家更新:', data)
  }

  /**
   * 处理系统维护通知
   */
  private handleSystemMaintenance(data: SystemMessageData): void {
    console.log('🔧 系统维护通知:', data)

    import('./notificationService').then(({ NotificationService }) => {
      const notificationService = NotificationService.getInstance()
      notificationService.showMaintenanceNotice(data)
    })
  }

  /**
   * 处理版本更新通知
   */
  private handleVersionUpdate(data: SystemMessageData): void {
    console.log('🆙 版本更新通知:', data)
  }

  /**
   * 处理服务公告
   */
  private handleServiceAnnouncement(data: SystemMessageData): void {
    console.log('📢 服务公告:', data)
  }

  // ==================== Store更新辅助方法 ====================

  /**
   * 更新订单store
   */
  private updateOrderStore(orderId: string, status: string): void {
    try {
      // 动态导入store避免循环依赖
      import('@/store')
        .then(({ useOrderStore }) => {
          const orderStore = useOrderStore()
          if (orderStore.updateOrderStatus) {
            orderStore.updateOrderStatus(orderId, status)
          }
        })
        .catch(() => {
          console.log('订单store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新订单store失败:', error)
    }
  }

  /**
   * 更新聊天store
   */
  private updateChatStore(data: ChatMessageData): void {
    try {
      import('@/store')
        .then(({ useChatStore }) => {
          const chatStore = useChatStore()
          if (chatStore.addMessage) {
            chatStore.addMessage(data)
          }
        })
        .catch(() => {
          console.log('聊天store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新聊天store失败:', error)
    }
  }

  /**
   * 更新优惠券store
   */
  private updateCouponStore(coupon: any): void {
    try {
      import('@/store')
        .then(({ useCouponStore }) => {
          const couponStore = useCouponStore()
          if (couponStore.addCoupon) {
            couponStore.addCoupon(coupon)
          }
        })
        .catch(() => {
          console.log('优惠券store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新优惠券store失败:', error)
    }
  }

  /**
   * 更新用户余额
   */
  private updateUserBalance(balance: number): void {
    try {
      import('@/store')
        .then(({ useUserStore }) => {
          const userStore = useUserStore()
          if (userStore.updateBalance) {
            userStore.updateBalance(balance)
          }
        })
        .catch(() => {
          console.log('用户store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新用户余额失败:', error)
    }
  }

  /**
   * 更新用户积分
   */
  private updateUserPoints(points: number): void {
    try {
      import('@/store')
        .then(({ useUserStore }) => {
          const userStore = useUserStore()
          if (userStore.updatePoints) {
            userStore.updatePoints(points)
          }
        })
        .catch(() => {
          console.log('用户store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新用户积分失败:', error)
    }
  }

  /**
   * 请求离线消息
   */
  private requestOfflineMessages(): void {
    // 可以在这里发送请求获取离线消息的指令
    console.log('📬 请求离线消息...')
  }

  // ==================== 跑腿员消息处理方法 ====================

  /**
   * 处理跑腿员任务分配通知
   */
  private handleRunnerTaskAssigned(data: any): void {
    console.log('🏃‍♂️ 处理跑腿员任务分配:', data)
    // 可以在这里添加特定的业务逻辑，比如更新任务列表
    this.updateRunnerTaskList()
  }

  /**
   * 处理跑腿员任务状态更新
   */
  private handleRunnerTaskStatusUpdate(data: any): void {
    console.log('📋 处理跑腿员任务状态更新:', data)
    this.updateRunnerTaskList()
  }

  /**
   * 处理跑腿员任务取消通知
   */
  private handleRunnerTaskCancelled(data: any): void {
    console.log('❌ 处理跑腿员任务取消:', data)
    this.updateRunnerTaskList()
  }

  /**
   * 处理跑腿员收益通知
   */
  private handleRunnerEarnings(data: any): void {
    console.log('💰 处理跑腿员收益通知:', data)
    this.updateRunnerEarnings()
  }

  /**
   * 处理跑腿员提现通知
   */
  private handleRunnerWithdrawal(data: any): void {
    console.log('🏦 处理跑腿员提现通知:', data)
    this.updateRunnerEarnings()
  }

  /**
   * 处理跑腿员日收益通知
   */
  private handleRunnerDailyEarnings(data: any): void {
    console.log('📊 处理跑腿员日收益通知:', data)
    this.updateRunnerEarnings()
  }

  /**
   * 处理跑腿员状态变更通知
   */
  private handleRunnerStatusChange(data: any): void {
    console.log('🔄 处理跑腿员状态变更:', data)
    this.updateRunnerStatus(data)
  }

  /**
   * 处理跑腿员位置更新通知
   */
  private handleRunnerLocationUpdate(data: any): void {
    console.log('📍 处理跑腿员位置更新:', data)
    // 位置更新通常不需要显示通知，只需要更新内部状态
  }

  /**
   * 更新跑腿员任务列表
   */
  private updateRunnerTaskList(): void {
    try {
      import('@/store')
        .then(({ useRunnerStore }) => {
          const runnerStore = useRunnerStore()
          if (runnerStore && runnerStore.refreshTaskList) {
            runnerStore.refreshTaskList()
          }
        })
        .catch(() => {
          console.log('跑腿员store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新跑腿员任务列表失败:', error)
    }
  }

  /**
   * 更新跑腿员收益信息
   */
  private updateRunnerEarnings(): void {
    try {
      import('@/store')
        .then(({ useRunnerStore }) => {
          const runnerStore = useRunnerStore()
          if (runnerStore && runnerStore.refreshEarnings) {
            runnerStore.refreshEarnings()
          }
        })
        .catch(() => {
          console.log('跑腿员store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新跑腿员收益信息失败:', error)
    }
  }

  /**
   * 更新跑腿员状态
   */
  private updateRunnerStatus(data: any): void {
    try {
      import('@/store')
        .then(({ useRunnerStore }) => {
          const runnerStore = useRunnerStore()
          if (runnerStore && runnerStore.updateStatus) {
            runnerStore.updateStatus(data.new_status)
          }
        })
        .catch(() => {
          console.log('跑腿员store不存在或方法不可用')
        })
    } catch (error) {
      console.error('更新跑腿员状态失败:', error)
    }
  }
}
