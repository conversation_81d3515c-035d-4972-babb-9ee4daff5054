/**
 * 购物车状态管理模块
 * 管理购物车商品的增删改查等状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getCartList,
  addToCart,
  updateCartItem,
  removeCartItem,
  clearCart,
  selectCartItems,
  getCartCount,
  getCartStats,
  getCartByMerchant,
  updateCartItemRemark,
  validateCartItems,
  clearInvalidItems,
} from '@/api/cart'
import type {
  ICartItem,
  IAddToCartParams,
  IUpdateCartParams,
  ICartStats,
  IMerchantCartGroup,
} from '@/api/cart.typings'
import { toast } from '@/utils/toast'
import {
  getDeliveryConfig,
  type IDeliveryConfig,
  type IDeliveryFeeCalculateResponse,
} from '@/api/delivery'
import { calculateDeliveryFee as calculateDeliveryFeeNew } from '@/utils/deliveryFeeCalculator'
import { useAddressStore } from '@/store/address'

export const useCartStore = defineStore(
  'cart',
  () => {
    // 购物车商品列表
    const cartItems = ref<ICartItem[]>([])
    // 购物车商品总数
    const cartTotal = ref(0)
    // 选中商品数量
    const selectedCount = ref(0)
    // 总金额
    const totalAmount = ref(0)
    // 选中商品总金额
    const selectedAmount = ref(0)
    // 是否正在加载
    const loading = ref(false)

    // 扩展状态
    const cartStats = ref<ICartStats>({
      totalItems: 0,
      totalQuantity: 0,
      selectedItems: 0,
      selectedQuantity: 0,
      unselectedItems: 0,
      unselectedQuantity: 0,
      totalAmount: 0,
      selectedAmount: 0,
      merchantCount: 0,
    })

    // 按商家分组的购物车
    const merchantGroups = ref<IMerchantCartGroup[]>([])

    // 缓存相关
    const lastFetchTime = ref(0)
    const cacheExpiry = 5 * 60 * 1000 // 5分钟缓存

    // 错误状态
    const error = ref<string | null>(null)
    const invalidItems = ref<number[]>([]) // 无效商品ID列表

    // 配送费相关
    const deliveryConfig = ref<IDeliveryConfig | null>(null)
    const deliveryFeeResults = ref<Map<number, IDeliveryFeeCalculateResponse>>(new Map())

    // 计算属性：购物车是否为空
    const isEmpty = computed(() => cartItems.value.length === 0)

    // 计算属性：是否全选
    const isAllSelected = computed(() => {
      if (isEmpty.value) return false
      return cartItems.value.every((item) => item.selected)
    })

    // 计算属性：选中的商品列表
    const selectedItems = computed(() => cartItems.value.filter((item) => item.selected))

    // 计算属性：购物车角标数量
    const badgeCount = computed(() => {
      return cartItems.value.reduce((total, item) => total + item.quantity, 0)
    })

    // 计算属性：是否有无效商品
    const hasInvalidItems = computed(() => invalidItems.value.length > 0)

    // 计算属性：缓存是否有效
    const isCacheValid = computed(() => {
      return Date.now() - lastFetchTime.value < cacheExpiry
    })

    // 计算属性：总包装费
    const totalPackagingFee = computed(() => {
      return cartItems.value.reduce((total, item) => total + (item.packagingFee || 0), 0)
    })

    // 计算属性：选中商品的包装费
    const selectedPackagingFee = computed(() => {
      return selectedItems.value.reduce((total, item) => total + (item.packagingFee || 0), 0)
    })

    /**
     * 获取购物车列表（带缓存）
     */
    const fetchCartList = async (forceRefresh = false) => {
      try {
        // 检查缓存
        if (!forceRefresh && isCacheValid.value && cartItems.value.length > 0) {
          return
        }

        loading.value = true
        error.value = null

        const response = await getCartList()
        console.log('购物车列表:', response)

        // 检查响应数据是否有效
        if (!response || !response.data) {
          console.warn('购物车API返回空数据，可能是token过期或其他错误')
          cartItems.value = []
          cartTotal.value = 0
          selectedCount.value = 0
          totalAmount.value = 0
          selectedAmount.value = 0
          return
        }

        const { data } = response

        // 处理后端返回的数据结构
        if (Array.isArray(data)) {
          // 转换字段名
          cartItems.value = data.map((item) => ({
            id: item.cart_item_id,
            userId: 0,
            productId: item.food_id,
            productTitle: item.food_name,
            productImage: item.food_image,
            productPrice: item.price,
            originalPrice: item.original_price,
            specificationId: item.variant_id,
            specificationName: item.variant_name,
            quantity: item.quantity,
            selected: item.selected,
            merchantId: item.merchant_id,
            merchantName: item.merchant_name,
            merchantLongitude: item.merchant_longitude,
            merchantLatitude: item.merchant_latitude,
            supportPickup: item.support_pickup || 0,
            stock: 999,
            available: true,
            packagingFee: item.packaging_fee,
            subtotal: item.subtotal,
            comboSelections: item.combo_selections,
            remark: item.remark || '',
            promotionInfo: item.promotion_info,
            promotions: item.promotions || [],
            createTime: '',
            updateTime: '',
          }))

          // 按商家分组
          merchantGroups.value = groupCartItemsByMerchant(cartItems.value)

          // 计算统计信息
          cartTotal.value = cartItems.value.length
          selectedCount.value = cartItems.value.filter((item) => item.selected).length
          totalAmount.value = cartItems.value.reduce((sum, item) => sum + item.subtotal, 0)
          selectedAmount.value = cartItems.value
            .filter((item) => item.selected)
            .reduce((sum, item) => sum + item.subtotal, 0)
        } else {
          // 兼容对象格式
          cartItems.value = data.list || []
          cartTotal.value = data.total || 0
          selectedCount.value = data.selectedCount || 0
          totalAmount.value = data.totalAmount || 0
          selectedAmount.value = data.selectedAmount || 0
        }

        // 更新缓存时间
        lastFetchTime.value = Date.now()

        // 验证商品有效性
        await validateCart()

        // 计算配送费
        await calculateAllDeliveryFees()
      } catch (error) {
        console.error('获取购物车列表失败:', error)
        error.value = '获取购物车列表失败'
        toast.error('获取购物车列表失败')
      } finally {
        loading.value = false
      }
    }

    /**
     * 获取购物车统计信息
     */
    const fetchCartStats = async () => {
      try {
        const { data } = await getCartStats()
        cartStats.value = data
        return data
      } catch (error) {
        console.error('获取购物车统计失败:', error)
        toast.error('获取购物车统计失败')
      }
    }

    /**
     * 获取按商家分组的购物车
     */
    const fetchMerchantGroups = async () => {
      try {
        const { data } = await getCartByMerchant()
        merchantGroups.value = data
        return data
      } catch (error) {
        console.error('获取商家分组失败:', error)
        toast.error('获取商家分组失败')
      }
    }

    /**
     * 验证购物车商品有效性
     */
    const validateCart = async () => {
      try {
        const { data } = await validateCartItems()

        // 确保 invalidItems 是数组，处理各种可能的数据格式
        if (data && data.invalidItems) {
          invalidItems.value = Array.isArray(data.invalidItems) ? data.invalidItems : []
        } else {
          invalidItems.value = []
        }

        // 标记无效商品
        if (cartItems.value && cartItems.value.length > 0) {
          cartItems.value.forEach((item) => {
            item.available = !invalidItems.value.includes(item.id)
          })
        }

        return data || { invalidItems: [] }
      } catch (error) {
        console.error('验证购物车失败:', error)
        // 出错时重置无效商品列表并将所有商品标记为可用
        invalidItems.value = []
        if (cartItems.value && cartItems.value.length > 0) {
          cartItems.value.forEach((item) => {
            item.available = true
          })
        }
        return { invalidItems: [] }
      }
    }

    /**
     * 添加商品到购物车
     * @param params 添加参数
     */
    const addItemToCart = async (params: IAddToCartParams) => {
      try {
        await addToCart(params)
        toast.success('已添加到购物车')
        // 重新获取购物车列表，强制刷新
        await fetchCartList(true)
        // 更新购物车数量
        await updateCartCount()
      } catch (error) {
        console.error('添加到购物车失败:', error)
        toast.error('添加到购物车失败')
      }
    }

    /**
     * 更新购物车商品数量
     * @param params 更新参数
     */
    const updateCartItemQuantity = async (params: IUpdateCartParams) => {
      try {
        console.log('开始更新购物车商品数量:', params)
        await updateCartItem(params)
        console.log('购物车商品数量更新成功，开始重新获取购物车列表')
        // 重新获取购物车列表，强制刷新
        await fetchCartList(true)
        console.log('购物车列表重新获取完成')
      } catch (error) {
        console.error('更新购物车失败:', error)
        toast.error('更新购物车失败')
        throw error // 重新抛出错误，让调用方知道操作失败
      }
    }

    /**
     * 删除单个购物车商品
     * @param cartItemId 购物车商品ID
     */
    const removeCartItemById = async (cartItemId: number) => {
      try {
        await removeCartItem(cartItemId)
        toast.success('删除成功')
        // 重新获取购物车列表，强制刷新
        await fetchCartList(true)
        // 更新购物车数量
        await updateCartCount()
      } catch (error) {
        console.error('删除购物车商品失败:', error)
        toast.error('删除失败')
      }
    }

    /**
     * 批量删除购物车商品
     * @param cartItemIds 购物车商品ID数组
     */
    const removeCartItemsByIds = async (cartItemIds: number[]) => {
      try {
        // 由于后端没有批量删除接口，通过多次调用单个删除接口实现
        for (const cartItemId of cartItemIds) {
          await removeCartItem(cartItemId)
        }
        toast.success('删除成功')
        // 重新获取购物车列表，强制刷新
        await fetchCartList(true)
        // 更新购物车数量
        await updateCartCount()
      } catch (error) {
        console.error('删除购物车商品失败:', error)
        toast.error('删除失败')
      }
    }

    /**
     * 清空购物车
     */
    const clearCartItems = async () => {
      try {
        await clearCart()
        toast.success('购物车已清空')
        // 清空本地状态
        cartItems.value = []
        cartTotal.value = 0
        selectedCount.value = 0
        totalAmount.value = 0
        selectedAmount.value = 0
        // 更新购物车数量
        await updateCartCount()
      } catch (error) {
        console.error('清空购物车失败:', error)
        toast.error('清空购物车失败')
      }
    }

    /**
     * 选择/取消选择购物车商品
     * @param cartItemIds 购物车商品ID数组
     * @param selected 是否选中
     */
    const selectCartItemsByIds = async (cartItemIds: number[], selected: boolean) => {
      try {
        await selectCartItems(cartItemIds, selected)
        // 重新获取购物车列表，强制刷新
        await fetchCartList(true)
      } catch (error) {
        console.error('选择购物车商品失败:', error)
        toast.error('操作失败')
      }
    }

    /**
     * 全选/取消全选购物车商品
     * @param selected 是否全选
     */
    const selectAllCartItemsAction = async (selected: boolean) => {
      try {
        // 获取所有购物车项的ID
        const cartItemIds = cartItems.value.map((item) => item.id)

        if (cartItemIds.length === 0) {
          console.log('购物车为空，无需操作')
          return
        }

        // 调用选择API
        await selectCartItems(cartItemIds, selected)

        // 重新获取购物车列表，强制刷新
        await fetchCartList(true)
      } catch (error) {
        console.error('全选购物车商品失败:', error)
        toast.error('操作失败')
      }
    }

    /**
     * 更新购物车数量（用于角标显示）
     */
    const updateCartCount = async () => {
      try {
        const { data } = await getCartCount()
        // 这里可以用于更新tabbar角标等
        uni.setTabBarBadge({
          index: 2, // 假设购物车是第3个tab
          text: data.count > 0 ? data.count.toString() : '',
        })
      } catch (error) {
        console.error('获取购物车数量失败:', error)
      }
    }

    /**
     * 批量删除购物车商品（通过多次调用单个删除接口实现）
     */
    const batchRemoveCartItems = async (cartItemIds: number[]) => {
      try {
        // 由于后端没有批量删除接口，通过多次调用单个删除接口实现
        for (const cartItemId of cartItemIds) {
          await removeCartItem(cartItemId)
        }
        toast.success('批量删除成功')
        await fetchCartList(true)
        await updateCartCount()
      } catch (error) {
        console.error('批量删除失败:', error)
        toast.error('批量删除失败')
      }
    }

    /**
     * 更新商品备注
     */
    const updateItemRemark = async (cartItemId: number, remark: string) => {
      try {
        await updateCartItemRemark(cartItemId, remark)

        // 更新本地状态
        const item = cartItems.value.find((item) => item.id === cartItemId)
        if (item) {
          item.remark = remark
        }

        toast.success('备注更新成功')
      } catch (error) {
        console.error('更新备注失败:', error)
        toast.error('更新备注失败')
      }
    }

    /**
     * 清除无效商品
     */
    const clearInvalidCartItems = async () => {
      try {
        await clearInvalidItems()
        toast.success('已清除无效商品')
        await fetchCartList(true)
        await updateCartCount()
      } catch (error) {
        console.error('清除无效商品失败:', error)
        toast.error('清除无效商品失败')
      }
    }

    /**
     * 智能刷新（根据缓存状态决定是否刷新）
     */
    const smartRefresh = async () => {
      if (!isCacheValid.value) {
        await fetchCartList(true)
      }
    }

    /**
     * 按商家分组购物车商品
     */
    const groupCartItemsByMerchant = (items: ICartItem[]): IMerchantCartGroup[] => {
      const groups = new Map<number, IMerchantCartGroup>()

      items.forEach((item) => {
        if (!groups.has(item.merchantId)) {
          groups.set(item.merchantId, {
            merchantId: item.merchantId,
            merchantName: item.merchantName,
            merchantLongitude: item.merchantLongitude,
            merchantLatitude: item.merchantLatitude,
            minOrderAmount: 20, // 默认起送金额，可以从商家信息获取
            deliveryFee: 3, // 默认配送费，可以从商家信息获取
            items: [],
            subtotal: 0,
            packagingFee: 0,
            selectedSubtotal: 0,
            selectedPackagingFee: 0,
            canCheckout: false,
            selectedCount: 0,
            totalCount: 0,
            promotions: item.promotions || [],
            remark: '',
            supportPickup: item.supportPickup || 0, // 是否支持到店自取
            deliveryMethod: 'delivery', // 默认配送方式为配送
          })
        }

        const group = groups.get(item.merchantId)!
        group.items.push(item)
        group.totalCount++
        group.subtotal += item.subtotal
        group.packagingFee += item.packagingFee || 0

        if (item.selected) {
          group.selectedCount++
          group.selectedSubtotal += item.subtotal
          group.selectedPackagingFee += item.packagingFee || 0
        }

        // 更新促销活动（合并去重）
        if (item.promotions && item.promotions.length > 0) {
          item.promotions.forEach((promotion) => {
            if (!group.promotions.find((p) => p.id === promotion.id)) {
              group.promotions.push(promotion)
            }
          })
        }
      })

      // 检查是否满足起送条件
      groups.forEach((group) => {
        group.canCheckout = group.selectedSubtotal >= group.minOrderAmount
      })

      return Array.from(groups.values())
    }

    /**
     * 获取配送费配置
     */
    const fetchDeliveryConfig = async () => {
      try {
        const config = await getDeliveryConfig()
        console.log('🚚 获取配送费配置成功:', config)
        deliveryConfig.value = config
        return config
      } catch (error) {
        console.error('获取配送费配置失败:', error)
        return null
      }
    }

    /**
     * 检查并获取用户地址
     */
    const checkAndFetchUserAddress = async () => {
      const addressStore = useAddressStore()

      console.log('🏠 检查用户地址状态...')
      console.log('🏠 当前默认地址:', addressStore.defaultAddress)
      console.log('🏠 地址列表长度:', addressStore.addressList.length)

      // 如果没有默认地址，尝试获取
      if (!addressStore.defaultAddress) {
        console.log('🏠 没有默认地址，尝试获取默认地址...')
        await addressStore.fetchDefaultAddress()
      }

      // 如果地址列表为空，获取地址列表
      if (addressStore.addressList.length === 0) {
        console.log('🏠 地址列表为空，获取地址列表...')
        await addressStore.fetchAddressList()
      }

      console.log('🏠 地址检查完成，默认地址:', addressStore.defaultAddress)
      return addressStore.defaultAddress
    }

    /**
     * 计算所有商家的配送费
     */
    const calculateAllDeliveryFees = async () => {
      console.log('🚚 开始计算配送费...')

      if (!deliveryConfig.value) {
        console.log('🚚 配送费配置未加载，开始获取配置...')
        await fetchDeliveryConfig()
      }

      if (!deliveryConfig.value) {
        console.warn('🚚 配送费配置未加载，无法计算配送费')
        return
      }

      // 检查并获取用户地址
      const defaultAddress = await checkAndFetchUserAddress()

      let userLat: number | undefined
      let userLng: number | undefined

      if (defaultAddress) {
        // 尝试多种可能的坐标字段名
        const lat = defaultAddress.location_latitude || defaultAddress.locationLatitude
        const lng = defaultAddress.location_longitude || defaultAddress.locationLongitude

        if (lat && lng) {
          userLat = Number(lat)
          userLng = Number(lng)
          console.log('🏠 使用默认地址坐标:', { userLat, userLng })
        } else {
          console.log('🏠 默认地址没有坐标信息:', defaultAddress)
        }
      } else {
        console.log('🏠 未找到默认地址')
      }

      // 准备商家数据，包含坐标信息
      const merchantData = merchantGroups.value.map((group) => ({
        merchantId: group.merchantId,
        merchantName: group.merchantName,
        selectedSubtotal: group.selectedSubtotal,
        merchantLatitude: group.merchantLatitude,
        merchantLongitude: group.merchantLongitude,
      }))

      console.log('🚚 商家数据:', merchantData)

      // 更新配送费结果
      deliveryFeeResults.value.clear()

      // 为每个商家计算配送费
      for (const group of merchantGroups.value) {
        try {
          console.log(`🚚 计算商家${group.merchantId}的配送费，订单金额: ${group.selectedSubtotal}`)

          const result = await calculateDeliveryFeeNew({
            merchantId: group.merchantId,
            merchantName: group.merchantName,
            merchantLat: group.merchantLatitude,
            merchantLng: group.merchantLongitude,
            userLat,
            userLng,
            orderAmount: group.selectedSubtotal,
            scenario: 'cart', // 标识为购物车场景
          })

          // 转换为兼容的格式
          const compatibleResult: IDeliveryFeeCalculateResponse = {
            deliveryFee: result.deliveryFee,
            originalFee: result.originalFee,
            discountAmount: result.originalFee - result.deliveryFee,
            isFree: result.freeDelivery,
            distance: result.distance,
          }

          deliveryFeeResults.value.set(group.merchantId, compatibleResult)

          // 更新商家分组中的配送费信息
          group.deliveryFee = result.deliveryFee
          console.log(`🚚 商家${group.merchantId}配送费: ${result.deliveryFee}元`)
        } catch (error) {
          console.error(`🚚 计算商家${group.merchantId}配送费失败:`, error)

          // 使用默认配送费
          const defaultResult: IDeliveryFeeCalculateResponse = {
            deliveryFee: 5.0,
            originalFee: 5.0,
            discountAmount: 0,
            isFree: false,
            distance: 0,
          }

          deliveryFeeResults.value.set(group.merchantId, defaultResult)
          group.deliveryFee = 5.0
        }
      }

      console.log('🚚 配送费计算完成')
    }

    /**
     * 获取指定商家的配送费结果
     */
    const getDeliveryFeeResult = (merchantId: number): IDeliveryFeeCalculateResponse | null => {
      return deliveryFeeResults.value.get(merchantId) || null
    }

    /**
     * 本地选择单个商品
     * @param cartItemId 购物车商品ID
     * @param selected 是否选中
     */
    const selectCartItemLocal = (cartItemId: number, selected: boolean) => {
      const item = cartItems.value.find((item) => item.id === cartItemId)
      if (item) {
        item.selected = selected
        // 重新计算选中数量和金额
        calculateSelectedInfo()
      }
    }

    /**
     * 本地全选/取消全选
     * @param selected 是否全选
     */
    const selectAllCartItemsLocal = (selected: boolean) => {
      cartItems.value.forEach((item) => {
        item.selected = selected
      })
      // 重新计算选中数量和金额
      calculateSelectedInfo()
    }

    /**
     * 计算选中商品信息
     */
    const calculateSelectedInfo = () => {
      const selected = cartItems.value.filter((item) => item.selected)
      selectedCount.value = selected.length
      selectedAmount.value = selected.reduce(
        (total, item) => total + item.productPrice * item.quantity,
        0,
      )
    }

    /**
     * 重置购物车状态
     */
    const resetCart = () => {
      cartItems.value = []
      cartTotal.value = 0
      selectedCount.value = 0
      totalAmount.value = 0
      selectedAmount.value = 0
    }

    return {
      // 基础状态
      cartItems,
      cartTotal,
      selectedCount,
      totalAmount,
      selectedAmount,
      loading,

      // 扩展状态
      cartStats,
      merchantGroups,
      error,
      invalidItems,
      lastFetchTime,

      // 计算属性
      isEmpty,
      isAllSelected,
      selectedItems,
      badgeCount,
      hasInvalidItems,
      isCacheValid,
      totalPackagingFee,
      selectedPackagingFee,

      // 基础方法
      fetchCartList,
      addItemToCart,
      updateCartItemQuantity,
      removeCartItemById,
      removeCartItemsByIds,
      clearCartItems,
      selectCartItemsByIds,
      selectAllCartItemsAction,
      updateCartCount,
      selectCartItemLocal,
      selectAllCartItemsLocal,
      calculateSelectedInfo,
      resetCart,

      // 扩展方法
      fetchCartStats,
      fetchMerchantGroups,
      validateCart,
      batchRemoveCartItems,
      updateItemRemark,
      clearInvalidCartItems,
      smartRefresh,

      // 配送费相关
      deliveryConfig: readonly(deliveryConfig),
      deliveryFeeResults: readonly(deliveryFeeResults),
      fetchDeliveryConfig,
      calculateAllDeliveryFees,
      getDeliveryFeeResult,
      checkAndFetchUserAddress,
    }
  },
  {
    persist: {
      paths: ['cartItems', 'cartTotal', 'selectedCount', 'totalAmount', 'selectedAmount'],
    },
  },
)
