/**
 * uni-app scroll-view 被动事件监听器警告修复
 *
 * 这是一个专门针对 uni-app H5 平台 scroll-view 组件的修复方案
 * 解决 "Unable to preventDefault inside passive event listener invocation" 警告
 */

/**
 * 完全屏蔽 uni-app scroll-view 的被动事件警告
 */
export function suppressUniScrollViewWarnings() {
  if (typeof window === 'undefined') return

  console.log('🔧 开始屏蔽 uni-app scroll-view 被动事件警告...')

  // 保存原始的控制台方法
  const originalWarn = console.warn
  const originalError = console.error
  const originalLog = console.log

  // 重写 console.warn
  console.warn = function (...args: any[]) {
    const message = args.join(' ')

    // 屏蔽特定的警告
    if (
      message.includes('Unable to preventDefault inside passive event listener') ||
      message.includes('__handleScroll') ||
      message.includes('Unhandled error during execution of activated hook') ||
      message.includes('ScrollView') ||
      (message.includes('passive') && message.includes('preventDefault'))
    ) {
      // 完全忽略这些警告
      return
    }

    // 其他警告正常输出
    originalWarn.apply(console, args)
  }

  // 重写 console.error
  console.error = function (...args: any[]) {
    const message = args.join(' ')

    // 屏蔽特定的错误
    if (
      message.includes('Unable to preventDefault inside passive event listener') ||
      message.includes('__handleScroll') ||
      message.includes('Cannot set properties of null') ||
      message.includes('scrollLeft') ||
      message.includes('scrollTop') ||
      (message.includes('passive') && message.includes('preventDefault'))
    ) {
      // 完全忽略这些错误
      return
    }

    // 其他错误正常输出
    originalError.apply(console, args)
  }

  console.log('✅ uni-app scroll-view 被动事件警告屏蔽已启用')
}

/**
 * 重写 uni-app 的 __handleScroll 方法
 */
export function patchUniHandleScroll() {
  if (typeof window === 'undefined') return

  console.log('🔧 开始修补 uni-app __handleScroll 方法...')

  // 等待 uni-app 加载完成
  const checkAndPatch = () => {
    // 查找 uni-app 的全局对象
    const uniGlobal = (window as any).uni || (window as any).__uniConfig

    if (uniGlobal) {
      console.log('🔍 找到 uni-app 全局对象，开始修补...')

      // 尝试修补可能的 __handleScroll 方法
      const patchScrollHandler = (obj: any, path: string = '') => {
        if (!obj || typeof obj !== 'object') return

        for (const key in obj) {
          if (key === '__handleScroll' && typeof obj[key] === 'function') {
            console.log(`🔧 找到 __handleScroll 方法: ${path}.${key}`)

            const originalHandler = obj[key]
            obj[key] = function (...args: any[]) {
              try {
                return originalHandler.apply(this, args)
              } catch (e) {
                // 静默处理 preventDefault 错误
                if (e.message && e.message.includes('preventDefault')) {
                  return
                }
                throw e
              }
            }
          } else if (typeof obj[key] === 'object' && obj[key] !== null) {
            // 递归查找
            patchScrollHandler(obj[key], path ? `${path}.${key}` : key)
          }
        }
      }

      patchScrollHandler(uniGlobal, 'uni')
    }

    // 修补可能存在的全局 __handleScroll
    if ((window as any).__handleScroll) {
      console.log('🔧 修补全局 __handleScroll 方法')

      const originalGlobalHandler = (window as any).__handleScroll
      ;(window as any).__handleScroll = function (...args: any[]) {
        try {
          return originalGlobalHandler.apply(this, args)
        } catch (e) {
          if (e.message && e.message.includes('preventDefault')) {
            return
          }
          throw e
        }
      }
    }
  }

  // 立即尝试修补
  checkAndPatch()

  // 延迟修补，确保 uni-app 完全加载
  setTimeout(checkAndPatch, 100)
  setTimeout(checkAndPatch, 500)
  setTimeout(checkAndPatch, 1000)

  console.log('✅ uni-app __handleScroll 修补已启动')
}

/**
 * 重写 addEventListener 来处理 scroll-view 的事件
 */
export function patchScrollViewEventListeners() {
  if (typeof window === 'undefined') return

  console.log('🔧 开始修补 scroll-view 事件监听器...')

  // 保存原始的 addEventListener
  const originalAddEventListener = Element.prototype.addEventListener

  // 重写 addEventListener
  Element.prototype.addEventListener = function (
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions,
  ) {
    // 如果是 scroll-view 相关的元素
    if (
      this.tagName === 'UNI-SCROLL-VIEW' ||
      this.classList.contains('uni-scroll-view') ||
      this.classList.contains('merchant-scroll')
    ) {
      // 如果是触摸事件，强制设置为被动
      if (type === 'touchstart' || type === 'touchmove' || type === 'touchend') {
        const newOptions =
          typeof options === 'object' ? { ...options, passive: true } : { passive: true }

        // 包装监听器，捕获 preventDefault 错误
        const wrappedListener = (event: Event) => {
          try {
            if (typeof listener === 'function') {
              listener(event)
            } else if (listener && typeof listener.handleEvent === 'function') {
              listener.handleEvent(event)
            }
          } catch (e) {
            // 静默处理 preventDefault 相关错误
            if (e.message && e.message.includes('preventDefault')) {
              return
            }
            throw e
          }
        }

        return originalAddEventListener.call(this, type, wrappedListener, newOptions)
      }
    }

    // 其他情况正常处理
    return originalAddEventListener.call(this, type, listener, options)
  }

  console.log('✅ scroll-view 事件监听器修补已完成')
}

/**
 * 直接修改 scroll-view 的 DOM 行为
 */
export function optimizeScrollViewDOM() {
  if (typeof window === 'undefined') return

  console.log('🔧 开始优化 scroll-view DOM 行为...')

  const optimizeElement = (element: HTMLElement) => {
    if (!element || element.dataset.scrollOptimized) return

    // 标记已优化
    element.dataset.scrollOptimized = 'true'

    // 移除可能导致问题的事件监听器
    const events = ['touchstart', 'touchmove', 'touchend', 'scroll']
    events.forEach((eventType) => {
      // 移除现有的事件监听器（如果可能）
      element.removeEventListener(eventType, () => {})

      // 添加新的被动事件监听器
      element.addEventListener(
        eventType,
        (e) => {
          // 不调用 preventDefault，让浏览器处理
        },
        { passive: true },
      )
    })

    // 设置 CSS 样式
    element.style.setProperty('-webkit-overflow-scrolling', 'touch', 'important')
    element.style.setProperty('overflow-scrolling', 'touch', 'important')
    element.style.setProperty('will-change', 'scroll-position', 'important')
    element.style.setProperty('contain', 'layout style paint', 'important')

    console.log('✅ 已优化 scroll-view 元素:', element.className)
  }

  // 优化现有元素
  const scrollViews = document.querySelectorAll(
    'uni-scroll-view, .uni-scroll-view, .merchant-scroll',
  )
  scrollViews.forEach((element) => optimizeElement(element as HTMLElement))

  // 监听新添加的元素
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element

          if (
            element.matches &&
            element.matches('uni-scroll-view, .uni-scroll-view, .merchant-scroll')
          ) {
            optimizeElement(element as HTMLElement)
          }

          // 查找子元素
          if (element.querySelectorAll) {
            const childScrollViews = element.querySelectorAll(
              'uni-scroll-view, .uni-scroll-view, .merchant-scroll',
            )
            childScrollViews.forEach((child) => optimizeElement(child as HTMLElement))
          }
        }
      })
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  console.log('✅ scroll-view DOM 优化已完成')
}

/**
 * 修复 scrollLeft/scrollTop 属性设置错误
 */
export function patchScrollProperties() {
  if (typeof window === 'undefined') return

  console.log('🔧 开始修补 scroll 属性设置...')

  // 保护 scrollLeft 和 scrollTop 属性设置
  const patchScrollProperty = (element: HTMLElement, property: 'scrollLeft' | 'scrollTop') => {
    const descriptor =
      Object.getOwnPropertyDescriptor(element, property) ||
      Object.getOwnPropertyDescriptor(Element.prototype, property)

    if (descriptor && descriptor.set) {
      const originalSetter = descriptor.set

      Object.defineProperty(element, property, {
        ...descriptor,
        set: function (value: number) {
          try {
            // 确保元素存在且可滚动
            if (this && typeof value === 'number' && !isNaN(value)) {
              originalSetter.call(this, value)
            }
          } catch (error) {
            // 静默处理设置错误
            console.debug(`忽略 ${property} 设置错误:`, error)
          }
        },
      })
    }
  }

  // 监听 DOM 变化，为新的 scroll-view 元素添加保护
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement

          // 检查是否是 scroll-view 相关元素
          if (
            element.tagName === 'UNI-SCROLL-VIEW' ||
            element.classList.contains('uni-scroll-view') ||
            element.classList.contains('merchant-scroll') ||
            element.querySelector('uni-scroll-view, .uni-scroll-view, .merchant-scroll')
          ) {
            // 为元素及其子元素添加保护
            const scrollElements = [element, ...element.querySelectorAll('*')]
            scrollElements.forEach((el) => {
              if (el instanceof HTMLElement) {
                patchScrollProperty(el, 'scrollLeft')
                patchScrollProperty(el, 'scrollTop')
              }
            })
          }
        }
      })
    })
  })

  // 开始观察
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  })

  // 为现有元素添加保护
  const existingElements = document.querySelectorAll(
    'uni-scroll-view, .uni-scroll-view, .merchant-scroll, *',
  )
  existingElements.forEach((element) => {
    if (element instanceof HTMLElement) {
      patchScrollProperty(element, 'scrollLeft')
      patchScrollProperty(element, 'scrollTop')
    }
  })

  console.log('✅ scroll 属性设置修补已完成')
}

/**
 * 修复 Vue 组件生命周期错误
 */
export function patchVueLifecycleErrors() {
  if (typeof window === 'undefined') return

  console.log('🔧 开始修补 Vue 组件生命周期错误...')

  // 全局错误处理器
  window.addEventListener(
    'error',
    (event) => {
      const error = event.error
      if (error && error.message) {
        // 屏蔽 scroll-view 相关的生命周期错误
        if (
          error.message.includes('Cannot set properties of null') ||
          error.message.includes('scrollLeft') ||
          error.message.includes('scrollTop') ||
          error.message.includes('ScrollView')
        ) {
          event.preventDefault()
          event.stopPropagation()
          console.debug('忽略 scroll-view 生命周期错误:', error.message)
          return false
        }
      }
    },
    true,
  )

  // 全局未处理的 Promise 拒绝处理器
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason
    if (reason && reason.message) {
      // 屏蔽 scroll-view 相关的 Promise 错误
      if (
        reason.message.includes('Cannot set properties of null') ||
        reason.message.includes('scrollLeft') ||
        reason.message.includes('scrollTop') ||
        reason.message.includes('ScrollView')
      ) {
        event.preventDefault()
        console.debug('忽略 scroll-view Promise 错误:', reason.message)
        return false
      }
    }
  })

  console.log('✅ Vue 组件生命周期错误修补已完成')
}

/**
 * 应用所有 uni-app scroll-view 修复
 */
export function applyAllUniScrollViewFixes() {
  console.log('🚀 开始应用所有 uni-app scroll-view 修复...')

  // 1. 屏蔽警告信息
  suppressUniScrollViewWarnings()

  // 2. 修补 __handleScroll 方法
  patchUniHandleScroll()

  // 3. 修补事件监听器
  patchScrollViewEventListeners()

  // 4. 优化 DOM 行为
  optimizeScrollViewDOM()

  // 5. 修补 scroll 属性设置
  patchScrollProperties()

  // 6. 修补 Vue 组件生命周期错误
  patchVueLifecycleErrors()

  console.log('✅ 所有 uni-app scroll-view 修复已应用')
}

/**
 * 检查修复效果
 */
export function checkScrollViewFixStatus() {
  console.log('🔍 检查 scroll-view 修复状态...')

  const scrollViews = document.querySelectorAll(
    'uni-scroll-view, .uni-scroll-view, .merchant-scroll',
  )
  console.log(`📊 找到 ${scrollViews.length} 个 scroll-view 元素`)

  scrollViews.forEach((element, index) => {
    const htmlElement = element as HTMLElement
    console.log(`📋 scroll-view ${index + 1}:`, {
      tagName: htmlElement.tagName,
      className: htmlElement.className,
      optimized: htmlElement.dataset.scrollOptimized === 'true',
      overflowScrolling: getComputedStyle(htmlElement).webkitOverflowScrolling,
    })
  })

  return scrollViews.length > 0
}
