/**
 * 配送方式转换工具
 * 
 * 用于在前端配送方式字符串和后端deliveryType数字之间进行转换
 */

/**
 * 配送方式枚举
 */
export enum DeliveryType {
  DELIVERY = 0, // 配送订单
  PICKUP = 2,   // 自提订单
}

/**
 * 前端配送方式类型
 */
export type DeliveryMethod = 'delivery' | 'pickup'

/**
 * 将前端配送方式转换为后端deliveryType数字
 * @param method 前端配送方式
 * @returns 后端deliveryType数字
 */
export function convertDeliveryMethodToType(method: DeliveryMethod): number {
  switch (method) {
    case 'pickup':
      return DeliveryType.PICKUP
    case 'delivery':
    default:
      return DeliveryType.DELIVERY
  }
}

/**
 * 将后端deliveryType数字转换为前端配送方式
 * @param type 后端deliveryType数字
 * @returns 前端配送方式
 */
export function convertDeliveryTypeToMethod(type: number): DeliveryMethod {
  switch (type) {
    case DeliveryType.PICKUP:
      return 'pickup'
    case DeliveryType.DELIVERY:
    default:
      return 'delivery'
  }
}

/**
 * 获取配送方式的中文描述
 * @param method 配送方式
 * @returns 中文描述
 */
export function getDeliveryMethodText(method: DeliveryMethod): string {
  switch (method) {
    case 'pickup':
      return '到店自取'
    case 'delivery':
      return '外卖配送'
    default:
      return '外卖配送'
  }
}

/**
 * 获取deliveryType的中文描述
 * @param type deliveryType数字
 * @returns 中文描述
 */
export function getDeliveryTypeText(type: number): string {
  switch (type) {
    case DeliveryType.PICKUP:
      return '自提订单'
    case DeliveryType.DELIVERY:
      return '配送订单'
    default:
      return '配送订单'
  }
}

/**
 * 验证deliveryType是否有效
 * @param type deliveryType数字
 * @returns 是否有效
 */
export function isValidDeliveryType(type: number): boolean {
  return type === DeliveryType.DELIVERY || type === DeliveryType.PICKUP
}

/**
 * 验证配送方式是否有效
 * @param method 配送方式
 * @returns 是否有效
 */
export function isValidDeliveryMethod(method: string): method is DeliveryMethod {
  return method === 'delivery' || method === 'pickup'
}
