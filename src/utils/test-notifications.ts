/**
 * 测试订单通知功能的工具函数
 * 用于模拟各种订单通知场景
 */

import { useWebSocketStore } from '@/store/websocket'
import { WSNotificationEventType } from '@/api/chat.typings'

/**
 * 模拟退款结果通知
 * 基于你提供的实际WebSocket消息格式
 */
export const simulateRefundNotification = () => {
  const wsStore = useWebSocketStore()

  // 模拟实际的退款通知消息
  const mockRefundMessage = {
    type: 'notification',
    event: 'user_refund_result',
    session_id: 0,
    data: {
      content: '您的退款申请处理结果：退款成功，您的退款申请已通过，退款金额 8.00 元已退回您的账户',
      data: {
        action: 'approve',
        action_type: 'order_detail',
        action_url: '/order/detail/84',
        complete_time: 1753427146,
        merchant_id: 1,
        message: '您的退款申请已通过，退款金额 8.00 元已退回您的账户',
        order_amount: 8,
        order_id: 84,
        order_no: '202507251442251419',
        process_remark: '',
        process_time: 1753427146,
        refund_amount: 8,
        refund_id: 10,
        refund_no: 'RF175342632284',
        refund_reason: '不想要了/拍错了',
        refund_remark: '',
        refund_status: 2,
        status: '退款成功',
        user_id: 2,
      },
      expire_time: 1753686347,
      order_id: 0,
      order_no: '',
      persistent: true,
      priority: 2,
      title: '退款处理结果',
      type: 'user_refund_result',
    },
    timestamp: 1753427147,
  }

  // 直接调用WebSocket消息处理函数
  const websocketService = (wsStore as any).websocketService
  if (websocketService) {
    websocketService.notifyListeners('notification', mockRefundMessage)
  }

  console.log('模拟退款通知已发送:', mockRefundMessage)
}

/**
 * 模拟订单支付成功通知
 */
export const simulatePaymentSuccessNotification = () => {
  const wsStore = useWebSocketStore()

  const mockPaymentMessage = {
    type: 'notification',
    event: 'order_payment_success',
    session_id: 0,
    data: {
      title: '支付成功',
      content: '您的订单支付成功，订单号：202507251442251419，支付金额：8.00元',
      order_id: 84,
      order_no: '202507251442251419',
      amount: 8.0,
      action_type: 'order_detail',
      action_url: '/order/detail/84',
      persistent: true,
      priority: 1,
    },
    timestamp: Date.now(),
  }

  const websocketService = (wsStore as any).websocketService
  if (websocketService) {
    websocketService.notifyListeners('notification', mockPaymentMessage)
  }

  console.log('模拟支付成功通知已发送:', mockPaymentMessage)
}

/**
 * 模拟订单发货通知
 */
export const simulateOrderShippedNotification = () => {
  const wsStore = useWebSocketStore()

  const mockShippedMessage = {
    type: 'notification',
    event: 'order_shipped',
    session_id: 0,
    data: {
      title: '订单已发货',
      content: '您的订单已发货，订单号：202507251442251419，请注意查收',
      order_id: 84,
      order_no: '202507251442251419',
      action_type: 'order_detail',
      action_url: '/order/detail/84',
      tracking_no: 'SF1234567890',
      courier_company: '顺丰快递',
      persistent: true,
      priority: 1,
    },
    timestamp: Date.now(),
  }

  const websocketService = (wsStore as any).websocketService
  if (websocketService) {
    websocketService.notifyListeners('notification', mockShippedMessage)
  }

  console.log('模拟发货通知已发送:', mockShippedMessage)
}

/**
 * 模拟订单取消通知
 */
export const simulateOrderCancelledNotification = () => {
  const wsStore = useWebSocketStore()

  const mockCancelledMessage = {
    type: 'notification',
    event: 'order_cancelled',
    session_id: 0,
    data: {
      title: '订单已取消',
      content: '您的订单已取消，订单号：202507251442251419',
      order_id: 84,
      order_no: '202507251442251419',
      cancel_reason: '用户主动取消',
      action_type: 'order_detail',
      action_url: '/order/detail/84',
      persistent: true,
      priority: 1,
    },
    timestamp: Date.now(),
  }

  const websocketService = (wsStore as any).websocketService
  if (websocketService) {
    websocketService.notifyListeners('notification', mockCancelledMessage)
  }

  console.log('模拟订单取消通知已发送:', mockCancelledMessage)
}

/**
 * 测试所有订单通知类型
 */
export const testAllOrderNotifications = () => {
  console.log('开始测试所有订单通知类型...')

  // 延时发送不同类型的通知，避免同时触发
  setTimeout(() => simulatePaymentSuccessNotification(), 1000)
  setTimeout(() => simulateOrderShippedNotification(), 2000)
  setTimeout(() => simulateRefundNotification(), 3000)
  setTimeout(() => simulateOrderCancelledNotification(), 4000)

  console.log('所有测试通知已安排发送')
}

// 在开发环境下暴露到全局，方便调试
if (import.meta.env.DEV) {
  ;(window as any).testNotifications = {
    simulateRefundNotification,
    simulatePaymentSuccessNotification,
    simulateOrderShippedNotification,
    simulateOrderCancelledNotification,
    testAllOrderNotifications,
  }

  console.log('测试通知工具已加载，可在控制台使用：')
  console.log('- window.testNotifications.simulateRefundNotification()')
  console.log('- window.testNotifications.simulatePaymentSuccessNotification()')
  console.log('- window.testNotifications.simulateOrderShippedNotification()')
  console.log('- window.testNotifications.simulateOrderCancelledNotification()')
  console.log('- window.testNotifications.testAllOrderNotifications()')
}
