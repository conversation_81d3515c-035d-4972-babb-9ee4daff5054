/**
 * WebSocket调试工具
 * 用于调试和验证WebSocket服务的功能
 */

import { webSocketService } from '@/services/websocket'

/**
 * 调试WebSocket服务状态
 */
export function debugWebSocketService() {
  console.log('🔍 WebSocket服务调试信息:')
  console.log('- 是否已初始化:', webSocketService.initialized)
  console.log('- 当前状态:', webSocketService.currentStatus)
  console.log('- 是否已连接:', webSocketService.isConnected)

  // 获取连接统计
  const stats = webSocketService.getConnectionStats()
  console.log('- 连接统计:', stats)

  // 检查环境变量
  console.log('- WebSocket URL:', import.meta.env.VITE_WEBSOCKET_URL)
  console.log('- 默认URL:', 'ws://localhost:8181/api/v1/chat/ws')
}

/**
 * 测试WebSocket连接URL构建
 */
export function testWebSocketURL() {
  const token = 'test_token_123'
  const deviceId = 'test_device_456'

  // 模拟URL构建
  const baseUrl = import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:8181/api/v1/chat/ws'
  const params = new URLSearchParams()
  params.append('token', token)
  params.append('device_id', deviceId)

  const fullUrl = `${baseUrl}?${params.toString()}`

  console.log('🔗 WebSocket URL测试:')
  console.log('- 基础URL:', baseUrl)
  console.log('- 完整URL:', fullUrl)
  console.log('- 包含token:', fullUrl.includes('token='))
  console.log('- 包含device_id:', fullUrl.includes('device_id='))

  return {
    baseUrl,
    fullUrl,
    hasToken: fullUrl.includes('token='),
    hasDeviceId: fullUrl.includes('device_id='),
    isValid: fullUrl.includes('token=') && fullUrl.includes('device_id='),
  }
}

/**
 * 检查设备信息
 */
export function checkDeviceInfo() {
  console.log('📱 设备信息检查:')

  // 检查存储的设备ID
  const deviceId = uni.getStorageSync('device_id')
  const currentDeviceId = uni.getStorageSync('current_device_id')

  console.log('- device_id:', deviceId || '未设置')
  console.log('- current_device_id:', currentDeviceId || '未设置')

  // 检查设备信息工具
  try {
    const { generateDeviceInfo } = require('@/utils/device')
    const deviceInfo = generateDeviceInfo()
    console.log('- 生成的设备信息:', deviceInfo)
  } catch (error) {
    console.error('- 设备信息工具错误:', error)
  }

  return {
    deviceId,
    currentDeviceId,
    hasDeviceId: !!(deviceId || currentDeviceId),
  }
}

/**
 * 检查用户登录状态
 */
export async function checkUserState() {
  try {
    const { useUserStore } = await import('@/store/user')
    const userStore = useUserStore()

    console.log('👤 用户状态检查:')
    console.log('- 是否已登录:', userStore.isLoggedIn)
    console.log('- 用户信息:', userStore.userInfo ? '已设置' : '未设置')
    console.log('- Token:', userStore.userInfo?.token ? '已设置' : '未设置')
    console.log('- RefreshToken:', userStore.userInfo?.refreshToken ? '已设置' : '未设置')

    return {
      isLoggedIn: userStore.isLoggedIn,
      hasUserInfo: !!userStore.userInfo,
      hasToken: !!userStore.userInfo?.token,
      hasRefreshToken: !!userStore.userInfo?.refreshToken,
    }
  } catch (error) {
    console.error('❌ 用户状态检查失败:', error)
    return null
  }
}

/**
 * 运行完整的诊断
 */
export async function runFullDiagnostic() {
  console.log('🏥 开始WebSocket服务完整诊断...')
  console.log('=====================================')

  // 1. 服务状态
  debugWebSocketService()
  console.log('')

  // 2. URL测试
  const urlTest = testWebSocketURL()
  console.log('')

  // 3. 设备信息
  const deviceInfo = checkDeviceInfo()
  console.log('')

  // 4. 用户状态
  const userState = await checkUserState()
  console.log('')

  // 5. 综合评估
  console.log('📊 诊断结果:')
  console.log('- WebSocket服务:', webSocketService.initialized ? '✅ 已初始化' : '❌ 未初始化')
  console.log('- URL构建:', urlTest.isValid ? '✅ 正常' : '❌ 异常')
  console.log('- 设备信息:', deviceInfo.hasDeviceId ? '✅ 正常' : '⚠️ 缺失')
  console.log('- 用户状态:', userState?.isLoggedIn ? '✅ 已登录' : '⚠️ 未登录')

  const overallStatus = webSocketService.initialized && urlTest.isValid && deviceInfo.hasDeviceId
  console.log('- 整体状态:', overallStatus ? '✅ 正常' : '❌ 需要修复')

  console.log('=====================================')
  console.log('🏥 诊断完成')

  return {
    service: webSocketService.initialized,
    url: urlTest.isValid,
    device: deviceInfo.hasDeviceId,
    user: userState?.isLoggedIn || false,
    overall: overallStatus,
  }
}

/**
 * 在控制台中快速调试
 */
export function quickDebug() {
  console.log('⚡ WebSocket快速调试:')
  debugWebSocketService()
  testWebSocketURL()
  checkDeviceInfo()
}

// 将调试函数挂载到全局对象，方便在控制台中调用
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.wsDebug = {
    debug: debugWebSocketService,
    testURL: testWebSocketURL,
    checkDevice: checkDeviceInfo,
    checkUser: checkUserState,
    fullDiagnostic: runFullDiagnostic,
    quick: quickDebug,
  }

  console.log('🔧 WebSocket调试工具已加载，可在控制台使用 wsDebug 对象')
}
