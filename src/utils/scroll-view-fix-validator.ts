/**
 * scroll-view 修复效果验证工具
 *
 * 用于验证各种修复是否正确应用
 */

/**
 * 验证控制台屏蔽是否生效
 */
export function validateConsoleSuppress(): boolean {
  console.log('🧪 测试控制台屏蔽效果...')

  let suppressWorking = true

  // 保存原始的 console.error
  const originalError = console.error

  // 临时重写 console.error 来检测是否被调用
  let errorCalled = false
  console.error = function (...args: any[]) {
    errorCalled = true
    originalError.apply(console, args)
  }

  try {
    // 模拟触发应该被屏蔽的错误
    const testErrors = [
      "Cannot set properties of null (setting 'scrollLeft')",
      'Unable to preventDefault inside passive event listener',
      '__handleScroll error test',
    ]

    testErrors.forEach((errorMsg) => {
      errorCalled = false

      // 触发错误（这些应该被屏蔽）
      try {
        throw new Error(errorMsg)
      } catch (e) {
        // 检查是否被屏蔽
        if (errorCalled) {
          console.warn(`⚠️ 错误未被屏蔽: ${errorMsg}`)
          suppressWorking = false
        }
      }
    })
  } finally {
    // 恢复原始的 console.error
    console.error = originalError
  }

  if (suppressWorking) {
    console.log('✅ 控制台屏蔽工作正常')
  } else {
    console.warn('❌ 控制台屏蔽可能未生效')
  }

  return suppressWorking
}

/**
 * 验证 scrollLeft/scrollTop 属性保护是否生效
 */
export function validateScrollPropertyProtection(): boolean {
  console.log('🧪 测试 scroll 属性保护效果...')

  let protectionWorking = true

  try {
    // 创建测试元素
    const testElement = document.createElement('div')
    testElement.className = 'test-scroll-element'
    document.body.appendChild(testElement)

    // 测试设置 scrollLeft 为 null（应该被保护）
    try {
      ;(testElement as any).scrollLeft = null
      console.log('✅ scrollLeft null 设置被安全处理')
    } catch (error) {
      console.warn('❌ scrollLeft null 设置未被保护:', error)
      protectionWorking = false
    }

    // 测试设置 scrollTop 为 undefined（应该被保护）
    try {
      ;(testElement as any).scrollTop = undefined
      console.log('✅ scrollTop undefined 设置被安全处理')
    } catch (error) {
      console.warn('❌ scrollTop undefined 设置未被保护:', error)
      protectionWorking = false
    }

    // 测试正常的数值设置（应该正常工作）
    try {
      testElement.scrollLeft = 100
      testElement.scrollTop = 50
      console.log('✅ 正常数值设置工作正常')
    } catch (error) {
      console.warn('❌ 正常数值设置失败:', error)
      protectionWorking = false
    }

    // 清理测试元素
    document.body.removeChild(testElement)
  } catch (error) {
    console.error('❌ scroll 属性保护测试失败:', error)
    protectionWorking = false
  }

  if (protectionWorking) {
    console.log('✅ scroll 属性保护工作正常')
  } else {
    console.warn('❌ scroll 属性保护可能未生效')
  }

  return protectionWorking
}

/**
 * 验证事件监听器修复是否生效
 */
export function validateEventListenerFix(): boolean {
  console.log('🧪 测试事件监听器修复效果...')

  let fixWorking = true

  try {
    // 创建测试 scroll-view 元素
    const testScrollView = document.createElement('div')
    testScrollView.className = 'merchant-scroll'
    testScrollView.tagName = 'UNI-SCROLL-VIEW' // 模拟 uni-scroll-view
    document.body.appendChild(testScrollView)

    // 测试添加触摸事件监听器
    let listenerCalled = false
    const testListener = () => {
      listenerCalled = true
    }

    try {
      testScrollView.addEventListener('touchmove', testListener)
      console.log('✅ touchmove 事件监听器添加成功')

      // 移除监听器
      testScrollView.removeEventListener('touchmove', testListener)
    } catch (error) {
      console.warn('❌ 事件监听器修复可能未生效:', error)
      fixWorking = false
    }

    // 清理测试元素
    document.body.removeChild(testScrollView)
  } catch (error) {
    console.error('❌ 事件监听器修复测试失败:', error)
    fixWorking = false
  }

  if (fixWorking) {
    console.log('✅ 事件监听器修复工作正常')
  } else {
    console.warn('❌ 事件监听器修复可能未生效')
  }

  return fixWorking
}

/**
 * 验证 DOM 优化是否应用
 */
export function validateDOMOptimization(): boolean {
  console.log('🧪 测试 DOM 优化效果...')

  let optimizationWorking = true

  try {
    // 查找页面中的 scroll-view 元素
    const scrollViews = document.querySelectorAll(
      'uni-scroll-view, .uni-scroll-view, .merchant-scroll',
    )

    if (scrollViews.length === 0) {
      console.log('ℹ️ 页面中暂无 scroll-view 元素，无法测试 DOM 优化')
      return true
    }

    scrollViews.forEach((element, index) => {
      const htmlElement = element as HTMLElement

      // 检查是否应用了优化样式
      const computedStyle = window.getComputedStyle(htmlElement)

      const checks = [
        { property: '-webkit-overflow-scrolling', expected: 'touch' },
        { property: 'will-change', expected: 'scroll-position' },
        { property: 'contain', expected: 'layout style paint' },
      ]

      checks.forEach((check) => {
        const value = computedStyle.getPropertyValue(check.property)
        if (!value.includes(check.expected)) {
          console.warn(`❌ 元素 ${index} 缺少优化样式 ${check.property}: ${check.expected}`)
          optimizationWorking = false
        }
      })
    })

    if (optimizationWorking) {
      console.log(`✅ DOM 优化已应用到 ${scrollViews.length} 个 scroll-view 元素`)
    }
  } catch (error) {
    console.error('❌ DOM 优化测试失败:', error)
    optimizationWorking = false
  }

  return optimizationWorking
}

/**
 * 运行所有验证测试
 */
export function runAllValidations(): {
  consoleSuppress: boolean
  scrollPropertyProtection: boolean
  eventListenerFix: boolean
  domOptimization: boolean
  overall: boolean
} {
  console.log('🚀 开始运行 scroll-view 修复验证测试...')

  const results = {
    consoleSuppress: validateConsoleSuppress(),
    scrollPropertyProtection: validateScrollPropertyProtection(),
    eventListenerFix: validateEventListenerFix(),
    domOptimization: validateDOMOptimization(),
    overall: false,
  }

  // 计算总体结果
  results.overall = Object.values(results).every((result) => result === true)

  console.log('📊 验证测试结果:')
  console.log('- 控制台屏蔽:', results.consoleSuppress ? '✅' : '❌')
  console.log('- 属性保护:', results.scrollPropertyProtection ? '✅' : '❌')
  console.log('- 事件监听器修复:', results.eventListenerFix ? '✅' : '❌')
  console.log('- DOM 优化:', results.domOptimization ? '✅' : '❌')
  console.log('- 总体状态:', results.overall ? '✅ 全部通过' : '❌ 部分失败')

  return results
}

/**
 * 在开发环境中自动运行验证（延迟执行，确保修复已应用）
 */
if (import.meta.env.DEV && typeof window !== 'undefined') {
  setTimeout(() => {
    runAllValidations()
  }, 2000)
}
