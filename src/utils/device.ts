/**
 * 设备信息工具函数
 * 用于生成多端登录所需的设备信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 */

/**
 * 设备信息接口
 */
export interface IDeviceInfo {
  device_id: string
  device_name: string
  device_type: 'mobile' | 'desktop' | 'tablet' | 'web'
  platform: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'web'
  browser: string
  app_version: string
  os_version: string
  user_agent: string
}

/**
 * 生成设备信息的工具函数
 * @returns {IDeviceInfo} 设备信息对象
 */
export function generateDeviceInfo(): IDeviceInfo {
  // 生成设备唯一标识
  const deviceId = getOrCreateDeviceId()

  // 获取设备信息
  const deviceInfo: IDeviceInfo = {
    device_id: deviceId,
    device_name: getDeviceName(),
    device_type: getDeviceType(),
    platform: getPlatform(),
    browser: getBrowserInfo(),
    app_version: getAppVersion(),
    os_version: getOSVersion(),
    user_agent: getUserAgent(),
  }

  return deviceInfo
}

/**
 * 获取或创建设备ID
 * @returns {string} 设备唯一标识
 */
function getOrCreateDeviceId(): string {
  let deviceId = uni.getStorageSync('device_id')
  if (!deviceId) {
    deviceId = generateUUID()
    uni.setStorageSync('device_id', deviceId)
  }
  return deviceId
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 获取设备类型
 * @returns {string} 设备类型
 */
function getDeviceType(): 'mobile' | 'desktop' | 'tablet' | 'web' {
  // #ifdef H5
  const userAgent = navigator.userAgent
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    return /iPad/.test(userAgent) ? 'tablet' : 'mobile'
  }
  return 'desktop'
  // #endif

  // #ifdef MP-WEIXIN
  return 'mobile'
  // #endif

  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  if (systemInfo.platform === 'ios' || systemInfo.platform === 'android') {
    return 'mobile'
  }
  return 'desktop'
  // #endif

  return 'web'
}

/**
 * 获取平台信息
 * @returns {string} 平台信息
 */
function getPlatform(): 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'web' {
  // #ifdef H5
  const userAgent = navigator.userAgent
  if (/iPhone|iPad/.test(userAgent)) return 'ios'
  if (/Android/.test(userAgent)) return 'android'
  if (/Windows/.test(userAgent)) return 'windows'
  if (/Mac/.test(userAgent)) return 'macos'
  if (/Linux/.test(userAgent)) return 'linux'
  return 'web'
  // #endif

  // #ifdef MP-WEIXIN
  const systemInfoWx = uni.getSystemInfoSync()
  if (systemInfoWx.platform === 'ios') return 'ios'
  if (systemInfoWx.platform === 'android') return 'android'
  return 'web'
  // #endif

  // #ifdef APP-PLUS
  const systemInfoApp = uni.getSystemInfoSync()
  if (systemInfoApp.platform === 'ios') return 'ios'
  if (systemInfoApp.platform === 'android') return 'android'
  return 'web'
  // #endif

  return 'web'
}

/**
 * 获取浏览器信息
 * @returns {string} 浏览器信息
 */
function getBrowserInfo(): string {
  // #ifdef H5
  const userAgent = navigator.userAgent
  if (/Chrome/.test(userAgent)) return 'Chrome'
  if (/Firefox/.test(userAgent)) return 'Firefox'
  if (/Safari/.test(userAgent)) return 'Safari'
  if (/Edge/.test(userAgent)) return 'Edge'
  return 'Unknown'
  // #endif

  // #ifdef MP-WEIXIN
  return 'WeChat'
  // #endif

  // #ifdef APP-PLUS
  return 'App'
  // #endif

  return 'Unknown'
}

/**
 * 获取设备名称
 * @returns {string} 设备名称
 */
function getDeviceName(): string {
  const platform = getPlatform()
  const browser = getBrowserInfo()

  // #ifdef MP-WEIXIN
  const systemInfoWx = uni.getSystemInfoSync()
  return `${systemInfoWx.brand || 'Unknown'} ${systemInfoWx.model || 'Device'}`
  // #endif

  // #ifdef APP-PLUS
  const systemInfoApp = uni.getSystemInfoSync()
  return `${systemInfoApp.brand || 'Unknown'} ${systemInfoApp.model || 'Device'}`
  // #endif

  return `${platform} ${browser}`
}

/**
 * 获取APP版本（如果是APP）
 * @returns {string} APP版本
 */
function getAppVersion(): string {
  // #ifdef APP-PLUS
  const systemInfoApp = uni.getSystemInfoSync()
  return systemInfoApp.appVersion || '1.0.0'
  // #endif

  // #ifdef MP-WEIXIN
  const systemInfoWx = uni.getSystemInfoSync()
  return systemInfoWx.version || '1.0.0'
  // #endif

  // 如果是Web，可以从package.json或配置获取
  return import.meta.env.VITE_APP_VERSION || '1.0.0'
}

/**
 * 获取操作系统版本
 * @returns {string} 操作系统版本
 */
function getOSVersion(): string {
  // #ifdef H5
  const userAgent = navigator.userAgent
  // 简化实现，实际可以更详细解析
  return userAgent.match(/\(([^)]+)\)/)?.[1] || 'Unknown'
  // #endif

  // #ifdef MP-WEIXIN
  const systemInfoWx = uni.getSystemInfoSync()
  return systemInfoWx.system || 'Unknown'
  // #endif

  // #ifdef APP-PLUS
  const systemInfoApp = uni.getSystemInfoSync()
  return systemInfoApp.system || 'Unknown'
  // #endif

  return 'Unknown'
}

/**
 * 获取用户代理字符串
 * @returns {string} 用户代理字符串
 */
function getUserAgent(): string {
  // #ifdef H5
  return navigator.userAgent
  // #endif

  // #ifdef MP-WEIXIN
  const systemInfoWx = uni.getSystemInfoSync()
  return `WeChat/${systemInfoWx.version} (${systemInfoWx.platform}; ${systemInfoWx.system})`
  // #endif

  // #ifdef APP-PLUS
  const systemInfoApp = uni.getSystemInfoSync()
  return `UniApp/${systemInfoApp.appVersion} (${systemInfoApp.platform}; ${systemInfoApp.system})`
  // #endif

  return 'Unknown'
}

/**
 * 显示新设备登录提示
 */
export function showNewDeviceNotification(): void {
  uni.showModal({
    title: '新设备登录',
    content: '检测到您在新设备上登录，请注意账户安全。',
    showCancel: false,
    confirmText: '知道了',
  })
}

/**
 * 显示风险警告
 * @param {number} riskLevel 风险等级 0-正常 1-低风险 2-中风险 3-高风险
 */
export function showRiskWarning(riskLevel: number): void {
  const messages: Record<number, string> = {
    1: '检测到低风险登录，请注意账户安全。',
    2: '检测到中风险登录，建议立即修改密码。',
    3: '检测到高风险登录，请立即修改密码并检查账户安全。',
  }

  const message = messages[riskLevel] || '检测到异常登录。'

  uni.showModal({
    title: '安全提醒',
    content: message,
    showCancel: false,
    confirmText: '知道了',
  })
}
