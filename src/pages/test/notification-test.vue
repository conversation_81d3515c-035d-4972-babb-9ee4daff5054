<template>
  <view class="notification-test">
    <wd-navbar title="通知功能测试" left-arrow @click-left="goBack" />

    <view class="test-container">
      <view class="test-section">
        <view class="section-title">基础通知测试</view>

        <wd-button type="primary" block @click="testBasicToast" style="margin-bottom: 10px">
          测试基础Toast通知
        </wd-button>

        <wd-button type="success" block @click="testSuccessToast" style="margin-bottom: 10px">
          测试成功Toast通知
        </wd-button>

        <wd-button type="warning" block @click="testWarningToast" style="margin-bottom: 10px">
          测试警告Toast通知
        </wd-button>

        <wd-button type="danger" block @click="testErrorToast" style="margin-bottom: 10px">
          测试错误Toast通知
        </wd-button>
      </view>

      <view class="test-section">
        <view class="section-title">WebSocket通知测试</view>

        <wd-button type="primary" block @click="testChatNotification" style="margin-bottom: 10px">
          测试聊天通知
        </wd-button>

        <wd-button
          type="warning"
          block
          @click="testImportantNotification"
          style="margin-bottom: 10px"
        >
          测试重要通知
        </wd-button>

        <wd-button type="danger" block @click="testUrgentNotification" style="margin-bottom: 10px">
          测试紧急通知
        </wd-button>
      </view>

      <view class="test-section">
        <view class="section-title">系统弹窗测试</view>

        <wd-button type="info" block @click="testModal" style="margin-bottom: 10px">
          测试系统弹窗
        </wd-button>

        <wd-button type="info" block @click="testActionSheet" style="margin-bottom: 10px">
          测试操作菜单
        </wd-button>
      </view>

      <view class="test-section">
        <view class="section-title">批量测试</view>

        <wd-button type="primary" block @click="runAllTests" style="margin-bottom: 10px">
          运行所有测试
        </wd-button>

        <wd-button type="info" block @click="checkComponents" style="margin-bottom: 10px">
          检查组件状态
        </wd-button>
      </view>

      <view class="test-section">
        <view class="section-title">测试日志</view>
        <view class="log-container">
          <view v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            {{ log.time }} - {{ log.message }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 测试日志
const logs = ref<Array<{ time: string; message: string; type: string }>>([])

// 添加日志
function addLog(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ time, message, type })

  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

// 返回上一页
function goBack() {
  uni.navigateBack()
}

// 基础Toast测试
function testBasicToast() {
  addLog('测试基础Toast通知', 'info')
  uni.showToast({
    title: '这是一个基础通知',
    icon: 'none',
    duration: 2000,
  })
}

function testSuccessToast() {
  addLog('测试成功Toast通知', 'success')
  uni.showToast({
    title: '操作成功！',
    icon: 'success',
    duration: 2000,
  })
}

function testWarningToast() {
  addLog('测试警告Toast通知', 'warning')
  uni.showToast({
    title: '请注意！',
    icon: 'none',
    duration: 2000,
  })
}

function testErrorToast() {
  addLog('测试错误Toast通知', 'error')
  uni.showToast({
    title: '操作失败！',
    icon: 'error',
    duration: 2000,
  })
}

// WebSocket通知测试
async function testChatNotification() {
  addLog('测试聊天通知', 'info')
  try {
    const { NotificationService } = await import('@/services/websocket/notificationService')
    const notificationService = NotificationService.getInstance()

    notificationService.showChatNotification({
      sender_name: '测试用户',
      content: '这是一条测试聊天消息',
      session_id: 'test-session-123',
    })

    addLog('聊天通知已发送', 'success')
  } catch (error) {
    addLog(`聊天通知失败: ${error}`, 'error')
  }
}

async function testImportantNotification() {
  addLog('测试重要通知', 'info')
  try {
    const { NotificationService } = await import('@/services/websocket/notificationService')
    const notificationService = NotificationService.getInstance()

    notificationService.showNotification({
      message: '这是一条重要通知',
      priority: 2,
    })

    addLog('重要通知已发送', 'success')
  } catch (error) {
    addLog(`重要通知失败: ${error}`, 'error')
  }
}

async function testUrgentNotification() {
  addLog('测试紧急通知', 'info')
  try {
    const { NotificationService } = await import('@/services/websocket/notificationService')
    const notificationService = NotificationService.getInstance()

    notificationService.showNotification({
      message: '这是一条紧急通知！',
      priority: 3,
    })

    addLog('紧急通知已发送', 'success')
  } catch (error) {
    addLog(`紧急通知失败: ${error}`, 'error')
  }
}

// 系统弹窗测试
function testModal() {
  addLog('测试系统弹窗', 'info')
  uni.showModal({
    title: '测试弹窗',
    content: '这是一个测试弹窗',
    showCancel: true,
    confirmText: '确定',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        addLog('用户点击了确定', 'success')
      } else {
        addLog('用户点击了取消', 'warning')
      }
    },
  })
}

function testActionSheet() {
  addLog('测试操作菜单', 'info')
  uni.showActionSheet({
    itemList: ['选项1', '选项2', '选项3'],
    success: (res) => {
      addLog(`用户选择了: ${res.tapIndex}`, 'success')
    },
    fail: () => {
      addLog('用户取消了操作', 'warning')
    },
  })
}

// 批量测试
async function runAllTests() {
  addLog('开始运行所有测试...', 'info')

  // 运行全局测试函数
  if (typeof (window as any).runFullNotifyTest === 'function') {
    await (window as any).runFullNotifyTest()
    addLog('全局测试完成', 'success')
  } else {
    addLog('全局测试函数不可用', 'warning')
  }

  // 依次测试各种通知
  setTimeout(() => testBasicToast(), 1000)
  setTimeout(() => testSuccessToast(), 2000)
  setTimeout(() => testChatNotification(), 3000)
  setTimeout(() => testImportantNotification(), 4000)

  addLog('所有测试已启动', 'success')
}

function checkComponents() {
  addLog('检查组件状态...', 'info')

  if (typeof (window as any).checkWotUIComponents === 'function') {
    const result = (window as any).checkWotUIComponents()
    addLog(`组件检查完成: ${JSON.stringify(result)}`, 'info')
  } else {
    addLog('组件检查函数不可用', 'warning')
  }
}

// 页面加载时添加欢迎日志
addLog('通知功能测试页面已加载', 'success')
</script>

<style lang="scss" scoped>
.notification-test {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.test-container {
  padding: 20px;
}

.test-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f8f8;
  border-radius: 4px;
  padding: 8px;
}

.log-item {
  font-size: 12px;
  line-height: 1.4;
  padding: 4px 0;
  border-bottom: 1px solid #eee;

  &.info {
    color: #666;
  }
  &.success {
    color: #52c41a;
  }
  &.warning {
    color: #faad14;
  }
  &.error {
    color: #f5222d;
  }

  &:last-child {
    border-bottom: none;
  }
}
</style>
