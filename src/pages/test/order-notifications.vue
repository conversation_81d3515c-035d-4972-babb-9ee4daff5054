<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '测试订单通知',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="test-page">
    <!-- 自定义导航栏 -->
    <wd-navbar title="测试订单通知" left-text="返回" left-arrow @click-left="handleBack" />

    <view class="test-content">
      <view class="test-section">
        <text class="section-title">订单通知测试</text>
        <text class="section-desc">点击下方按钮测试不同类型的订单通知</text>
      </view>

      <view class="test-buttons">
        <wd-button
          type="success"
          block
          @click="testRefundNotification"
          style="margin-bottom: 20rpx"
        >
          测试退款通知
        </wd-button>

        <wd-button type="primary" block @click="testPaymentSuccess" style="margin-bottom: 20rpx">
          测试支付成功通知
        </wd-button>

        <wd-button type="info" block @click="testOrderShipped" style="margin-bottom: 20rpx">
          测试发货通知
        </wd-button>

        <wd-button type="warning" block @click="testOrderCancelled" style="margin-bottom: 20rpx">
          测试订单取消通知
        </wd-button>

        <wd-button type="default" block @click="testAllNotifications" style="margin-bottom: 20rpx">
          测试所有通知类型
        </wd-button>
      </view>

      <view class="test-info">
        <text class="info-title">使用说明：</text>
        <text class="info-item">1. 点击按钮后会模拟相应的WebSocket通知</text>
        <text class="info-item">2. 通知会显示在系统消息中</text>
        <text class="info-item">3. 同时会更新相关订单状态</text>
        <text class="info-item">4. 可以在聊天列表中查看系统通知</text>
      </view>

      <view class="console-section">
        <text class="section-title">控制台命令</text>
        <text class="console-desc">也可以在浏览器控制台中使用以下命令：</text>
        <view class="console-commands">
          <text class="command">window.testNotifications.simulateRefundNotification()</text>
          <text class="command">window.testNotifications.simulatePaymentSuccessNotification()</text>
          <text class="command">window.testNotifications.simulateOrderShippedNotification()</text>
          <text class="command">window.testNotifications.simulateOrderCancelledNotification()</text>
          <text class="command">window.testNotifications.testAllOrderNotifications()</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  simulateRefundNotification,
  simulatePaymentSuccessNotification,
  simulateOrderShippedNotification,
  simulateOrderCancelledNotification,
  testAllOrderNotifications,
} from '@/utils/test-notifications'

// 返回
const handleBack = () => {
  uni.navigateBack()
}

// 测试退款通知
const testRefundNotification = () => {
  simulateRefundNotification()
  uni.showToast({
    title: '退款通知已发送',
    icon: 'success',
  })
}

// 测试支付成功通知
const testPaymentSuccess = () => {
  simulatePaymentSuccessNotification()
  uni.showToast({
    title: '支付成功通知已发送',
    icon: 'success',
  })
}

// 测试发货通知
const testOrderShipped = () => {
  simulateOrderShippedNotification()
  uni.showToast({
    title: '发货通知已发送',
    icon: 'success',
  })
}

// 测试订单取消通知
const testOrderCancelled = () => {
  simulateOrderCancelledNotification()
  uni.showToast({
    title: '取消通知已发送',
    icon: 'success',
  })
}

// 测试所有通知
const testAllNotifications = () => {
  testAllOrderNotifications()
  uni.showToast({
    title: '所有通知已安排发送',
    icon: 'success',
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.test-content {
  padding: 32rpx;
}

.test-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: center;

  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .section-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.test-buttons {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.test-info {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;

  .info-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .info-item {
    display: block;
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
    margin-bottom: 8rpx;
  }
}

.console-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;

  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .console-desc {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
  }

  .console-commands {
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 24rpx;

    .command {
      display: block;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 24rpx;
      color: #e83e8c;
      background: #f8f9fa;
      padding: 8rpx 0;
      word-break: break-all;
    }
  }
}
</style>
