<template>
  <view class="notification-test-page">
    <view class="header">
      <text class="title">WebSocket通知测试</text>
      <text class="subtitle">测试WebSocket消息通知功能</text>
    </view>

    <view class="test-content">
      <view class="test-section">
        <text class="section-title">聊天消息通知测试</text>
        <text class="section-desc">模拟接收到聊天消息时的通知显示</text>
      </view>

      <view class="test-buttons">
        <wd-button type="primary" block @click="testChatNotification" style="margin-bottom: 20rpx">
          测试聊天消息通知
        </wd-button>

        <wd-button type="success" block @click="testOrderNotification" style="margin-bottom: 20rpx">
          测试订单通知
        </wd-button>

        <wd-button
          type="warning"
          block
          @click="testUrgentNotification"
          style="margin-bottom: 20rpx"
        >
          测试紧急通知
        </wd-button>

        <wd-button type="info" block @click="testFallbackNotification" style="margin-bottom: 20rpx">
          测试降级通知
        </wd-button>

        <wd-button type="default" block @click="testAllNotifications" style="margin-bottom: 20rpx">
          测试所有通知类型
        </wd-button>
      </view>

      <view class="test-section">
        <text class="section-title">当前页面信息</text>
        <text class="section-desc">当前路由: {{ currentRoute }}</text>
        <text class="section-desc">是否在聊天页面: {{ isInChatPage ? '是' : '否' }}</text>
      </view>

      <view class="test-section">
        <text class="section-title">测试日志</text>
        <scroll-view class="log-container" scroll-y>
          <view v-for="(log, index) in testLogs" :key="index" class="log-item" :class="log.type">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-message">{{ log.message }}</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { NotificationService } from '@/services/websocket/notificationService'
import type { ChatMessageData } from '@/services/websocket/types'

// 响应式数据
const currentRoute = ref('')
const isInChatPage = ref(false)
const testLogs = ref<
  Array<{
    time: string
    message: string
    type: 'info' | 'success' | 'warning' | 'error'
  }>
>([])

// 通知服务实例
const notificationService = NotificationService.getInstance()

// 添加测试日志
const addLog = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

  testLogs.value.unshift({
    time,
    message,
    type,
  })

  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
}

// 获取当前页面信息
const getCurrentPageInfo = () => {
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    currentRoute.value = currentPage?.route || ''
    isInChatPage.value =
      currentRoute.value.includes('chat') || currentRoute.value.includes('message')

    addLog(`当前页面: ${currentRoute.value}`, 'info')
    addLog(`是否在聊天页面: ${isInChatPage.value}`, 'info')
  } catch (error) {
    addLog(`获取页面信息失败: ${error}`, 'error')
  }
}

// 测试聊天消息通知
const testChatNotification = () => {
  addLog('开始测试聊天消息通知...', 'info')

  const mockChatData: ChatMessageData = {
    session_id: '123',
    sender_name: '测试用户',
    content: '这是一条测试消息，用于验证通知功能是否正常工作',
    created_at: new Date().toISOString(),
    id: Date.now(),
    resource_id: '',
    sender_avatar: '',
    sender_id: 'test_user_123',
  }

  try {
    notificationService.showChatNotification(mockChatData)
    addLog('聊天消息通知已发送', 'success')
  } catch (error) {
    addLog(`聊天消息通知失败: ${error}`, 'error')
  }
}

// 测试订单通知
const testOrderNotification = () => {
  addLog('开始测试订单通知...', 'info')

  try {
    notificationService.showOrderNotification(
      'payment_success',
      '订单支付成功！您的订单正在处理中',
      'ORDER_123456',
    )
    addLog('订单通知已发送', 'success')
  } catch (error) {
    addLog(`订单通知失败: ${error}`, 'error')
  }
}

// 测试紧急通知
const testUrgentNotification = () => {
  addLog('开始测试紧急通知...', 'info')

  try {
    notificationService.showNotification({
      priority: 3,
      message: '这是一条紧急通知，请立即查看！',
      action_url: '/pages/test/websocket-notification',
      action_type: 'navigate',
    })
    addLog('紧急通知已发送', 'success')
  } catch (error) {
    addLog(`紧急通知失败: ${error}`, 'error')
  }
}

// 测试降级通知
const testFallbackNotification = () => {
  addLog('开始测试降级通知...', 'info')

  try {
    // 直接调用降级方法
    ;(notificationService as any).showFallbackToast('这是降级Toast通知，当WOT UI不可用时显示')
    addLog('降级通知已发送', 'success')
  } catch (error) {
    addLog(`降级通知失败: ${error}`, 'error')
  }
}

// 测试所有通知类型
const testAllNotifications = () => {
  addLog('开始测试所有通知类型...', 'info')

  const tests = [
    () => testChatNotification(),
    () => testOrderNotification(),
    () => testUrgentNotification(),
    () => testFallbackNotification(),
  ]

  tests.forEach((test, index) => {
    setTimeout(() => {
      test()
    }, index * 1000) // 每秒执行一个测试
  })
}

// 页面加载时获取当前页面信息
onMounted(() => {
  getCurrentPageInfo()
  addLog('WebSocket通知测试页面已加载', 'success')
})
</script>

<style lang="scss" scoped>
.notification-test-page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;

  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.test-content {
  .test-section {
    background: white;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    .section-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    .section-desc {
      display: block;
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 10rpx;
    }
  }

  .test-buttons {
    background: white;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .log-container {
    height: 400rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 20rpx;

    .log-item {
      padding: 16rpx;
      margin-bottom: 8rpx;
      border-radius: 8rpx;
      border-left: 6rpx solid #ddd;

      &.info {
        background: #e3f2fd;
        border-left-color: #2196f3;
      }

      &.success {
        background: #e8f5e8;
        border-left-color: #4caf50;
      }

      &.warning {
        background: #fff3e0;
        border-left-color: #ff9800;
      }

      &.error {
        background: #ffebee;
        border-left-color: #f44336;
      }

      .log-time {
        font-size: 24rpx;
        color: #999;
        margin-right: 20rpx;
      }

      .log-message {
        font-size: 26rpx;
        color: #333;
      }
    }
  }
}
</style>
