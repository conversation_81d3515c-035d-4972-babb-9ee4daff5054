<route lang="json5">
{
  style: {
    navigationBarTitleText: '购物车',
    navigationStyle: 'custom',
  },
  layout: 'tabbar',
}
</route>

<template>
  <view class="cart-container">
    <!-- 购物车列表 -->
    <view v-if="!isEmpty" class="cart-content">
      <!-- 收货地址选择器 -->
      <view class="address-section" @click="selectAddress">
        <view v-if="selectedAddress" class="address-info">
          <view class="address-header">
            <wd-icon name="location" size="20" color="#ff5500" />
            <view class="address-details">
              <view class="address-name-phone">
                <text class="name">{{ selectedAddress.receiver_name }}</text>
                <text class="phone">{{ selectedAddress.receiver_mobile }}</text>
              </view>
              <view class="address-text">{{ selectedAddress.full_address }}</view>
            </view>
          </view>
          <wd-icon name="arrow-right" size="16" color="#999" />
        </view>

        <view v-else class="no-address">
          <wd-icon name="location" size="20" color="#ff5500" />
          <text>请选择收货地址</text>
          <wd-icon name="arrow-right" size="16" color="#999" />
        </view>
      </view>

      <!-- 全选栏 -->
      <view class="select-all-bar">
        <wd-checkbox :model-value="isAllSelected" @update:model-value="handleSelectAll">
          全选
        </wd-checkbox>
        <view class="cart-count">共{{ totalCount }}件商品</view>
      </view>

      <!-- 按商家分组的购物车列表 -->
      <view class="merchant-groups">
        <view v-for="group in merchantGroups" :key="group.merchantId" class="merchant-group">
          <!-- 商家信息头部 -->
          <view class="merchant-header">
            <view class="merchant-info">
              <view class="merchant-name">{{ group.merchantName }}</view>
              <view class="merchant-stats">
                <text class="delivery-info">30-45分钟</text>
                <text class="divider">|</text>
                <text class="delivery-fee">配送费¥{{ group.deliveryFee.toFixed(2) }}</text>
              </view>
            </view>
            <view class="merchant-status">
              <text class="status-text open">营业中</text>
            </view>
          </view>

          <!-- 配送方式选择 -->
          <view v-if="group.supportPickup === 1" class="delivery-method-section">
            <view class="delivery-method-title">配送方式</view>
            <view class="delivery-method-options">
              <view
                class="delivery-method-option"
                :class="{ active: getDeliveryMethod(group.merchantId) === 'delivery' }"
                @click="setDeliveryMethod(group.merchantId, 'delivery')"
              >
                <view class="option-icon">🚚</view>
                <view class="option-content">
                  <view class="option-name">外卖配送</view>
                  <view class="option-desc">
                    配送费¥{{ formatDeliveryFeeForMethod(group.merchantId, 'delivery') }}
                  </view>
                </view>
                <view class="option-radio">
                  <wd-icon
                    :name="
                      getDeliveryMethod(group.merchantId) === 'delivery'
                        ? 'check-circle-filled'
                        : 'circle'
                    "
                    :color="getDeliveryMethod(group.merchantId) === 'delivery' ? '#ff5500' : '#ddd'"
                    size="20"
                  />
                </view>
              </view>

              <view
                class="delivery-method-option"
                :class="{ active: getDeliveryMethod(group.merchantId) === 'pickup' }"
                @click="setDeliveryMethod(group.merchantId, 'pickup')"
              >
                <view class="option-icon">🏪</view>
                <view class="option-content">
                  <view class="option-name">到店自取</view>
                  <view class="option-desc">无配送费，到店取餐</view>
                </view>
                <view class="option-radio">
                  <wd-icon
                    :name="
                      getDeliveryMethod(group.merchantId) === 'pickup'
                        ? 'check-circle-filled'
                        : 'circle'
                    "
                    :color="getDeliveryMethod(group.merchantId) === 'pickup' ? '#ff5500' : '#ddd'"
                    size="20"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 商家促销信息 -->
          <view v-if="group.promotions.length > 0" class="merchant-promotions">
            <view v-for="promotion in group.promotions" :key="promotion.id" class="promotion-item">
              <text class="promotion-tag">{{ promotion.type_name }}</text>
              <text class="promotion-text">{{ promotion.description }}</text>
            </view>
          </view>

          <!-- 优惠券选择器 - 只在有可用优惠券时显示 -->
          <view v-if="hasAvailableCoupons(group.merchantId)" class="coupon-section">
            <CouponSelector
              :merchant-id="group.merchantId"
              :total-amount="calculateMerchantSelectedAmount(group)"
              :food-ids="
                group.items
                  .filter((item) => item.selected)
                  .map((item) => item.productId)
                  .join(',')
              "
              @select="handleCouponSelect"
            />
          </view>

          <!-- 促销活动选择器 - 只在有可用促销活动时显示 -->
          <view v-if="hasAvailablePromotions(group.merchantId)" class="promotion-section">
            <MerchantPromotionSelector
              :merchant-id="group.merchantId"
              :total-amount="calculateMerchantSelectedAmount(group)"
              :food-ids="
                group.items
                  .filter((item) => item.selected)
                  .map((item) => item.productId)
                  .join(',')
              "
              @select="handlePromotionSelect"
            />
          </view>

          <!-- 无优惠信息提示 - 当有选中商品但既没有优惠券也没有促销活动时显示 -->
          <view
            v-if="
              group.items.some((item) => item.selected) &&
              !hasAvailableCoupons(group.merchantId) &&
              !hasAvailablePromotions(group.merchantId)
            "
            class="no-offers-tip"
          >
            <view class="tip-content">
              <text class="tip-icon">💡</text>
              <text class="tip-text">暂无可用的优惠券和促销活动</text>
            </view>
          </view>

          <!-- 商家商品列表 -->
          <view class="merchant-items">
            <view
              v-for="item in group.items"
              :key="item.id"
              class="cart-item"
              :class="{ disabled: !item.available }"
            >
              <!-- 选择框 -->
              <view class="item-checkbox" @click.stop>
                <wd-checkbox
                  :model-value="item.selected"
                  :disabled="!item.available"
                  @update:model-value="handleItemSelect(item.id, $event)"
                />
              </view>

              <!-- 商品信息 -->
              <view class="item-content" @click="goToProduct(item.productId)">
                <image :src="item.productImage" class="item-image" mode="aspectFill" />

                <view class="item-info">
                  <view class="item-title">{{ item.productTitle }}</view>

                  <!-- 规格信息 -->
                  <view v-if="item.specifications" class="item-specs">
                    {{ formatSpecs(item.specifications) }}
                  </view>
                  <view v-else-if="item.specificationName" class="item-specs">
                    {{ item.specificationName }}
                  </view>

                  <!-- 套餐信息 -->
                  <view
                    v-if="item.comboSelections && item.comboSelections.length > 0"
                    class="item-combos"
                  >
                    <view
                      v-for="combo in item.comboSelections"
                      :key="combo.comboId"
                      class="combo-item"
                    >
                      <text class="combo-name">{{ combo.comboName }}:</text>
                      <text class="combo-options">
                        {{ combo.selectedOptions.map((opt) => opt.optionName).join(', ') }}
                      </text>
                    </view>
                  </view>

                  <!-- 备注信息 -->
                  <view v-if="item.remark" class="item-remark">
                    <text class="remark-label">备注:</text>
                    <text class="remark-text">{{ item.remark }}</text>
                  </view>

                  <!-- 包装费 -->
                  <view v-if="item.packagingFee && item.packagingFee > 0" class="item-packaging">
                    <text class="packaging-label">包装费:</text>
                    <text class="packaging-fee">¥{{ item.packagingFee }}</text>
                  </view>

                  <view class="item-footer">
                    <view class="item-price">
                      <text class="current-price">¥{{ item.productPrice }}</text>
                      <text v-if="item.originalPrice" class="original-price">
                        ¥{{ item.originalPrice }}
                      </text>
                    </view>

                    <view class="item-actions">
                      <!-- 数量控制 -->
                      <view
                        class="quantity-control"
                        :class="{ disabled: !item.available }"
                        @click.stop
                      >
                        <view
                          class="quantity-btn decrease"
                          :class="{ disabled: item.quantity <= 1 || !item.available }"
                          @click.stop="handleQuantityDecrease(item.id)"
                        >
                          <wd-icon name="remove" size="16" color="#666" />
                        </view>
                        <view class="quantity-input">
                          <text class="quantity-text">{{ item.quantity }}</text>
                        </view>
                        <view
                          class="quantity-btn increase"
                          :class="{ disabled: item.quantity >= item.stock || !item.available }"
                          @click.stop="handleQuantityIncrease(item.id)"
                        >
                          <wd-icon name="add" size="16" color="#666" />
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 删除按钮 -->
              <view class="item-delete" @click.stop="handleDeleteItem(item.id)">
                <wd-icon name="delete" color="#999" size="20" />
              </view>

              <!-- 不可用遮罩 -->
              <view v-if="!item.available" class="unavailable-mask">
                <text class="unavailable-text">商品已下架</text>
              </view>
            </view>
          </view>

          <!-- 商家备注 -->
          <view class="merchant-remark">
            <view class="remark-title">
              <wd-icon name="edit" size="16" color="#ff5500" />
              <text>给商家留言</text>
            </view>
            <textarea
              v-model="group.remark"
              placeholder="选填，请输入您需要告诉商家的信息"
              class="remark-input"
              :maxlength="100"
            />
          </view>

          <!-- 商家小计 -->
          <view class="merchant-summary">
            <view class="summary-row">
              <text class="summary-label">商品小计</text>
              <text class="summary-value">¥{{ group.selectedSubtotal.toFixed(2) }}</text>
            </view>
            <view v-if="group.selectedPackagingFee > 0" class="summary-row">
              <text class="summary-label">包装费</text>
              <text class="summary-value">¥{{ group.selectedPackagingFee.toFixed(2) }}</text>
            </view>
            <!-- 配送费 - 只有当商家有选中商品时才显示 -->
            <view v-if="group.selectedSubtotal > 0" class="summary-row">
              <text class="summary-label">配送费</text>
              <text
                class="summary-value"
                :class="{
                  'free-delivery':
                    getDeliveryMethod(group.merchantId) === 'pickup' ||
                    getDeliveryFeeResult(group.merchantId)?.isFree,
                }"
              >
                {{
                  getDeliveryMethod(group.merchantId) === 'pickup'
                    ? '¥0.00 (自取)'
                    : formatDeliveryFeeTextForMerchant(group.merchantId)
                }}
              </text>
            </view>
            <!-- 促销折扣显示 -->
            <view
              v-if="group.promotions.length > 0 && calculatePromotionDiscount(group) > 0"
              class="summary-row"
            >
              <text class="summary-label">促销优惠</text>
              <text class="summary-value discount">
                -¥{{ calculatePromotionDiscount(group).toFixed(2) }}
              </text>
            </view>
            <!-- 优惠券折扣显示 -->
            <view v-if="getCouponDiscount(group.merchantId) > 0" class="summary-row">
              <text class="summary-label">优惠券</text>
              <text class="summary-value discount">
                -¥{{ getCouponDiscount(group.merchantId).toFixed(2) }}
              </text>
            </view>
            <!-- 促销活动折扣显示 -->
            <view v-if="getPromotionDiscount(group.merchantId) > 0" class="summary-row">
              <text class="summary-label">促销活动</text>
              <text class="summary-value discount">
                -¥{{ getPromotionDiscount(group.merchantId).toFixed(2) }}
              </text>
            </view>
            <!-- 配送费优惠提示 - 只有当商家有选中商品时才显示 -->
            <view
              v-if="group.selectedSubtotal > 0 && getDeliveryFeeTipForMerchant(group.merchantId)"
              class="delivery-tip"
            >
              <text class="tip-text">{{ getDeliveryFeeTipForMerchant(group.merchantId) }}</text>
            </view>

            <!-- 配送费调试信息 - 只有当商家有选中商品时才显示 -->
            <view
              v-if="group.selectedSubtotal > 0 && showDeliveryDebugInfo(group.merchantId)"
              class="delivery-debug"
            >
              <view class="debug-header">
                <text class="debug-title">🚚 配送费调试信息</text>
                <view class="debug-actions">
                  <view class="copy-btn" @click="copyDeliveryDebugInfo(group.merchantId)">
                    <wd-icon name="copy" size="16" color="#666" />
                    <text class="copy-text">复制</text>
                  </view>
                </view>
              </view>
              <view class="debug-content">
                <view
                  class="debug-item"
                  v-for="(info, index) in getDeliveryDebugInfo(group.merchantId)"
                  :key="index"
                >
                  <text class="debug-text">{{ info }}</text>
                </view>
              </view>
            </view>
            <view class="summary-row total">
              <text class="summary-label">小计</text>
              <text class="summary-value">¥{{ getMerchantTotal(group).toFixed(2) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空购物车 -->
    <view v-else class="empty-cart">
      <wd-status-tip
        image="cart"
        tip="购物车空空如也"
        :show-button="true"
        button-text="去逛逛"
        @button-click="goToHome"
      />
    </view>

    <!-- 底部结算栏 -->
    <view v-if="!isEmpty" class="bottom-bar">
      <view class="bar-left">
        <wd-checkbox :model-value="isAllSelected" @update:model-value="handleSelectAll">
          全选
        </wd-checkbox>
      </view>

      <view class="bar-right">
        <view class="price-info">
          <view class="total-info">
            <text class="total-label">合计:</text>
            <text class="total-price">¥{{ totalAmountWithDelivery.toFixed(2) }}</text>
          </view>
          <view v-if="selectedCount > 0" class="selected-count">
            已选{{ selectedCount }}件
            <text v-if="totalDeliveryFee > 0" class="delivery-fee-info">
              (含配送费¥{{ totalDeliveryFee.toFixed(2) }})
            </text>
          </view>
        </view>

        <wd-button type="primary" :disabled="selectedCount === 0" @click="handleCheckout">
          结算({{ selectedCount }})
        </wd-button>
      </view>
    </view>

    <!-- 地址选择弹窗 -->
    <wd-popup
      v-model="showAddressPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      :z-index="9999"
      :lock-scroll="true"
    >
      <view class="address-popup">
        <view class="popup-header">
          <view class="popup-title">选择收货地址</view>
          <wd-icon name="close" size="20" color="#999" @click="closeAddressSelector" />
        </view>

        <scroll-view scroll-y class="address-list">
          <view
            v-for="address in addressStore.addressList"
            :key="address.id"
            class="address-item"
            :class="{ selected: selectedAddress?.id === address.id }"
            @click="selectUserAddress(address)"
          >
            <view class="address-content">
              <view class="address-header">
                <text class="receiver">{{ address.receiver_name }}</text>
                <text class="phone">{{ address.receiver_mobile }}</text>
                <view v-if="address.is_default" class="default-tag">默认</view>
              </view>
              <view class="address-detail">
                {{ address.province }}{{ address.city }}{{ address.district
                }}{{ address.detailed_address }}
              </view>
            </view>
            <view v-if="selectedAddress?.id === address.id" class="address-selected">
              <wd-icon name="check" color="#ff5500" size="20" />
            </view>
          </view>

          <view v-if="addressStore.addressList.length === 0" class="no-address">
            <text>暂无收货地址，请添加</text>
          </view>
        </scroll-view>

        <view class="popup-footer">
          <view class="add-address-btn" @click="addNewAddress">
            <wd-icon name="add" size="16" color="#ff5500" />
            <text>新增地址</text>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 支付方式选择弹窗 -->
    <PaymentSelector
      v-model="showPaymentPopup"
      :payment-methods="paymentMethods"
      :order-summary="{
        itemAmount: totalAmount,
        deliveryFee: totalDeliveryFee,
        discount: 0,
        totalAmount: totalAmountWithDelivery,
      }"
      :loading="isCreatingOrder"
      @confirm="handlePaymentConfirm"
      @close="handlePaymentClose"
    />

    <!-- 删除确认弹窗 -->
    <wd-popup v-model="showDeleteModal" position="center">
      <view class="delete-modal">
        <view class="modal-title">确认删除</view>
        <view class="modal-content">确定要删除这件商品吗？</view>
        <view class="modal-actions">
          <wd-button type="default" @click="showDeleteModal = false">取消</wd-button>
          <wd-button type="primary" @click="confirmDelete">确定</wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- 清空购物车确认弹窗 -->
    <wd-popup v-model="showClearModal" position="center">
      <view class="clear-modal">
        <view class="modal-title">清空购物车</view>
        <view class="modal-content">确定要清空购物车吗？此操作不可恢复。</view>
        <view class="modal-actions">
          <wd-button type="default" @click="showClearModal = false">取消</wd-button>
          <wd-button type="primary" @click="confirmClear">确定</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useCartStore } from '@/store/cart'
import { useAddressStore } from '@/store/address'
import { useCouponStore } from '@/store/coupon'
import { usePromotionStore } from '@/store/promotion'
import CouponSelector from '@/components/coupon/CouponSelector.vue'
import MerchantPromotionSelector from '@/components/promotion/MerchantPromotionSelector.vue'
import PaymentSelector from '@/components/payment/PaymentSelector.vue'
import { useTakeoutStore } from '@/store/takeout'
import { formatDeliveryFeeText, getDeliveryFeeTip } from '@/api/delivery'
import { formatDistance } from '@/utils/distance'
import { createTakeoutOrder } from '@/api/takeout'

const cartStore = useCartStore()
const addressStore = useAddressStore()
const couponStore = useCouponStore()
const promotionStore = usePromotionStore()
const takeoutStore = useTakeoutStore()

// 购物车数据
const cartList = computed(() => cartStore.cartItems)
const totalCount = computed(() => cartStore.cartTotal)
const selectedCount = computed(() => cartStore.selectedCount)
const selectedTotalAmount = computed(() => cartStore.selectedAmount)
const isEmpty = computed(() => cartStore.isEmpty)
const loading = computed(() => cartStore.loading)

// 智能优惠算法相关状态
const bestOfferCombinations = ref<Record<number, BestOfferCombination>>({})

// 最优优惠组合类型定义
interface BestOfferCombination {
  merchantId: number
  bestCoupon: any | null
  bestPromotion: any | null
  totalSavings: number
  calculatedAt: number
}

// 商家分组数据
const merchantGroups = computed(() => cartStore.merchantGroups)

// 总配送费
const totalDeliveryFee = computed(() => {
  return merchantGroups.value.reduce((total, group) => {
    // 只有当商家有选中商品时才计算配送费
    if (group.selectedSubtotal <= 0) {
      console.log(`🚚 商家${group.merchantId}没有选中商品，跳过配送费计算`)
      return total
    }

    // 检查配送方式，如果是自取则配送费为0
    const deliveryMethod = getDeliveryMethod(group.merchantId)
    if (deliveryMethod === 'pickup') {
      console.log(`🚚 商家${group.merchantId}选择自取，配送费为0`)
      return total
    }

    const deliveryFeeResult = cartStore.getDeliveryFeeResult(group.merchantId)
    const deliveryFee = deliveryFeeResult ? deliveryFeeResult.deliveryFee : 3.0

    console.log(`🚚 商家${group.merchantId}配送费计算:`, {
      merchantName: group.merchantName,
      supportPickup: group.supportPickup,
      selectedSubtotal: group.selectedSubtotal,
      deliveryMethod,
      deliveryFee,
      hasSelectedItems: group.selectedSubtotal > 0,
    })

    return total + deliveryFee
  }, 0)
})

// 包含配送费和优惠券折扣的总金额
const totalAmountWithDelivery = computed(() => {
  // 计算所有商家的总计（包含优惠券折扣）
  const totalWithDiscounts = merchantGroups.value.reduce((total, group) => {
    return total + getMerchantTotal(group)
  }, 0)

  console.log('💰 总金额计算:', {
    selectedTotalAmount: selectedTotalAmount.value,
    totalDeliveryFee: totalDeliveryFee.value,
    totalWithDiscounts,
    merchantTotals: merchantGroups.value.map((group) => ({
      merchantId: group.merchantId,
      total: getMerchantTotal(group),
      couponDiscount: getCouponDiscount(group.merchantId),
    })),
  })

  return totalWithDiscounts
})

// 全选状态
const isAllSelected = computed(() => cartStore.isAllSelected)

// 弹窗状态
const showDeleteModal = ref(false)
const showClearModal = ref(false)
const deleteItemId = ref<number | null>(null)

// 地址相关状态
const showAddressPopup = ref(false)
const selectedAddress = computed(
  () =>
    addressStore.defaultAddress ||
    (addressStore.addressList.length > 0 ? addressStore.addressList[0] : null),
)

/**
 * 处理全选/取消全选
 */
const handleSelectAll = async (selected: any) => {
  try {
    // 处理wd-checkbox组件可能传递对象格式的问题
    let isSelected: boolean
    if (typeof selected === 'object' && selected !== null && 'value' in selected) {
      isSelected = selected.value
    } else {
      isSelected = Boolean(selected)
    }
    await cartStore.selectAllCartItemsAction(isSelected)

    // 全选状态变化后，重新验证所有商家的促销活动和优惠券
    if (isSelected) {
      console.log('🔄 全选商品，重新验证所有商家的促销活动和优惠券')
      const merchantIds = merchantGroups.value.map((group) => group.merchantId)
      await Promise.all(merchantIds.map((merchantId) => revalidateMerchantOffers(merchantId)))
    } else {
      console.log('🔄 取消全选，清除所有商家的促销活动和优惠券选择')
      const merchantIds = merchantGroups.value.map((group) => group.merchantId)
      merchantIds.forEach((merchantId) => {
        promotionStore.clearSelectedPromotion(merchantId)
        couponStore.clearSelectedCoupon(merchantId)
      })
    }
  } catch (error) {
    console.error('处理全选失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    })
  }
}

/**
 * 处理单个商品选择
 */
const handleItemSelect = async (itemId: number, selected: any) => {
  try {
    // 处理wd-checkbox组件可能传递对象格式的问题
    let isSelected: boolean
    if (typeof selected === 'object' && selected !== null && 'value' in selected) {
      isSelected = selected.value
    } else {
      isSelected = Boolean(selected)
    }
    await cartStore.selectCartItemsByIds([itemId], isSelected)

    // 商品选择状态变化后，重新验证相关商家的促销活动和优惠券
    const item = cartStore.cartItems.find((item) => item.id === itemId)
    if (item) {
      console.log(`🔄 商品选择状态变化，重新验证商家 ${item.merchantId} 的促销活动和优惠券`)
      await revalidateMerchantOffers(item.merchantId)
    }
  } catch (error) {
    console.error('处理商品选择失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    })
  }
}

/**
 * 处理商品数量减少
 */
const handleQuantityDecrease = async (cartItemId: number) => {
  const item = cartStore.cartItems.find((item) => item.id === cartItemId)
  if (!item || item.quantity <= 1 || !item.available) return

  try {
    await cartStore.updateCartItemQuantity({ cartItemId, quantity: item.quantity - 1 })

    // 数量变化后，如果商品是选中状态，需要重新验证促销活动和优惠券
    if (item.selected) {
      console.log(`🔄 商品数量减少，重新验证商家 ${item.merchantId} 的促销活动和优惠券`)
      await revalidateMerchantOffers(item.merchantId)
    }
  } catch (error) {
    uni.showToast({
      title: '更新失败',
      icon: 'error',
    })
  }
}

/**
 * 处理商品数量增加
 */
const handleQuantityIncrease = async (cartItemId: number) => {
  const item = cartStore.cartItems.find((item) => item.id === cartItemId)
  if (!item || item.quantity >= item.stock || !item.available) return

  try {
    await cartStore.updateCartItemQuantity({ cartItemId, quantity: item.quantity + 1 })

    // 数量变化后，如果商品是选中状态，需要重新验证促销活动和优惠券
    if (item.selected) {
      console.log(`🔄 商品数量增加，重新验证商家 ${item.merchantId} 的促销活动和优惠券`)
      await revalidateMerchantOffers(item.merchantId)
    }
  } catch (error) {
    uni.showToast({
      title: '更新失败',
      icon: 'error',
    })
  }
}

/**
 * 处理数量变化（保留原函数用于兼容）
 */
const handleQuantityChange = async (itemId: number, quantity: number) => {
  try {
    await cartStore.updateCartItemQuantity({ cartItemId: itemId, quantity })
  } catch (error) {
    uni.showToast({
      title: '更新失败',
      icon: 'error',
    })
  }
}

/**
 * 处理删除商品
 */
const handleDeleteItem = (itemId: number) => {
  deleteItemId.value = itemId
  showDeleteModal.value = true
}

/**
 * 确认删除商品
 */
const confirmDelete = async () => {
  if (!deleteItemId.value) return

  try {
    await cartStore.removeCartItemById(deleteItemId.value)
  } catch (error) {
    console.error('删除商品失败:', error)
  } finally {
    showDeleteModal.value = false
    deleteItemId.value = null
  }
}

/**
 * 清空购物车
 */
const handleClearCart = () => {
  showClearModal.value = true
}

/**
 * 确认清空购物车
 */
const confirmClear = async () => {
  try {
    await cartStore.clearCartItems()
    uni.showToast({
      title: '清空成功',
      icon: 'success',
    })
  } catch (error) {
    uni.showToast({
      title: '清空失败',
      icon: 'error',
    })
  } finally {
    showClearModal.value = false
  }
}

/**
 * 选择地址
 */
const selectAddress = async () => {
  // 确保地址列表已加载
  if (addressStore.addressList.length === 0) {
    await addressStore.fetchAddressList()
  }
  showAddressPopup.value = true
}

/**
 * 关闭地址选择器
 */
const closeAddressSelector = () => {
  showAddressPopup.value = false
}

/**
 * 选择用户地址
 */
const selectUserAddress = async (address: any) => {
  try {
    // 设置为默认地址
    await addressStore.setDefaultAddressById(address.id)

    // 重新计算配送费
    await cartStore.calculateAllDeliveryFees()

    console.log('🚚 地址选择后重新计算配送费完成')

    uni.showToast({
      title: '地址选择成功',
      icon: 'success',
      duration: 1500,
    })

    closeAddressSelector()
  } catch (error) {
    console.error('选择地址失败:', error)
    uni.showToast({
      title: '选择地址失败',
      icon: 'error',
    })
  }
}

/**
 * 新增地址
 */
const addNewAddress = () => {
  closeAddressSelector()
  uni.navigateTo({
    url: '/pages/user/address-edit',
  })
}

/**
 * 计算商家选中商品总金额
 */
const calculateMerchantSelectedAmount = (group: any) => {
  const amount = group.items
    .filter((item: any) => item.selected)
    .reduce(
      (total: number, item: any) => total + (item.subtotal || item.productPrice * item.quantity),
      0,
    )

  console.log(`🧮 计算商家${group.merchantId}选中金额:`, {
    merchantId: group.merchantId,
    selectedItems: group.items.filter((item: any) => item.selected).length,
    amount,
    items: group.items
      .filter((item: any) => item.selected)
      .map((item: any) => ({
        id: item.id,
        productId: item.productId,
        name: item.productTitle,
        price: item.productPrice,
        quantity: item.quantity,
        subtotal: item.subtotal,
      })),
  })

  return amount
}

/**
 * 检查使用优惠后是否会导致合计金额为负数
 */
const checkNegativeAmount = (
  merchantId: number,
  couponDiscount: number = 0,
  promotionDiscount: number = 0,
): boolean => {
  const group = merchantGroups.value.find((g) => g.merchantId === merchantId)
  if (!group) return false

  // 计算使用优惠后的金额
  const deliveryFeeResult = cartStore.getDeliveryFeeResult(merchantId)
  const deliveryFee = deliveryFeeResult ? deliveryFeeResult.deliveryFee : 3.0
  const baseAmount = group.selectedSubtotal + group.selectedPackagingFee + deliveryFee
  const finalAmount = baseAmount - couponDiscount - promotionDiscount

  console.log(`💰 商家${merchantId}负金额检查:`, {
    baseAmount,
    couponDiscount,
    promotionDiscount,
    finalAmount,
    wouldBeNegative: finalAmount < 0,
  })

  return finalAmount < 0
}

/**
 * 处理优惠券选择
 */
const handleCouponSelect = (merchantId: number, coupon: any) => {
  console.log('🎫 购物车页面 - 选择优惠券:', { merchantId, coupon })

  if (coupon) {
    // 检查使用该优惠券是否会导致合计金额为负数
    const couponDiscount = coupon.discount_amount || coupon.coupon.amount || 0
    const currentPromotionDiscount = getPromotionDiscount(merchantId)

    if (checkNegativeAmount(merchantId, couponDiscount, currentPromotionDiscount)) {
      console.log('🚫 优惠券使用后会导致合计金额为负数，禁止选择')
      uni.showToast({
        title: '该优惠券会导致金额异常，无法使用',
        icon: 'none',
        duration: 2000,
      })
      return
    }
  }

  // 调用couponStore的选择方法
  couponStore.selectCouponForMerchant(merchantId, coupon)

  // 显示选择结果
  if (coupon) {
    uni.showToast({
      title: `已选择优惠券: ${coupon.coupon.name}`,
      icon: 'success',
      duration: 2000,
    })
  } else {
    uni.showToast({
      title: '已取消选择优惠券',
      icon: 'none',
      duration: 1500,
    })
  }
}

/**
 * 处理促销活动选择
 */
const handlePromotionSelect = (merchantId: number, promotion: any) => {
  console.log('🎉 购物车页面 - 选择促销活动:', { merchantId, promotion })

  if (promotion) {
    // 检查使用该促销活动是否会导致合计金额为负数
    const promotionDiscount = promotion.discount_amount || 0
    const currentCouponDiscount = getCouponDiscount(merchantId)

    if (checkNegativeAmount(merchantId, currentCouponDiscount, promotionDiscount)) {
      console.log('🚫 促销活动使用后会导致合计金额为负数，禁止选择')
      uni.showToast({
        title: '该促销活动会导致金额异常，无法使用',
        icon: 'none',
        duration: 2000,
      })
      return
    }
  }

  // 调用promotionStore的选择方法
  promotionStore.selectPromotion(merchantId, promotion)

  // 显示选择结果
  if (promotion) {
    uni.showToast({
      title: `已选择促销活动: ${promotion.name}`,
      icon: 'success',
      duration: 2000,
    })
  } else {
    uni.showToast({
      title: '已取消选择促销活动',
      icon: 'none',
      duration: 1500,
    })
  }
}

/**
 * 跳转到外卖商品详情
 */
const goToProduct = (productId: number) => {
  uni.navigateTo({
    url: `/pages/takeout/food-detail?id=${productId}`,
  })
}

/**
 * 获取配送费结果
 */
const getDeliveryFeeResult = (merchantId: number) => {
  return cartStore.getDeliveryFeeResult(merchantId)
}

/**
 * 格式化配送费显示文本
 */
const formatDeliveryFeeTextForMerchant = (merchantId: number) => {
  // 检查商家是否有选中商品
  const group = merchantGroups.value.find((g) => g.merchantId === merchantId)
  if (!group || group.selectedSubtotal <= 0) {
    return '¥0.00' // 没有选中商品时配送费为0
  }

  const result = cartStore.getDeliveryFeeResult(merchantId)
  if (!result) {
    return '¥3.00' // 默认配送费
  }
  return formatDeliveryFeeText(result)
}

/**
 * 获取配送费优惠提示
 */
const getDeliveryFeeTipForMerchant = (merchantId: number) => {
  const config = cartStore.deliveryConfig
  const group = merchantGroups.value.find((g) => g.merchantId === merchantId)
  if (!config || !group) {
    return ''
  }
  return getDeliveryFeeTip(config, group.selectedSubtotal)
}

/**
 * 计算促销折扣
 */
const calculatePromotionDiscount = (group: any) => {
  if (!group.promotions || group.promotions.length === 0) {
    return 0
  }

  const selectedAmount = group.selectedSubtotal
  let totalDiscount = 0

  // 遍历所有促销活动
  group.promotions.forEach((promotion: any) => {
    if (promotion.type === 'full_reduction' && selectedAmount >= promotion.threshold) {
      totalDiscount += promotion.discount
    } else if (promotion.type === 'discount' && selectedAmount >= promotion.threshold) {
      totalDiscount += selectedAmount * (1 - promotion.discount / 100)
    }
  })

  return totalDiscount
}

/**
 * 获取优惠券折扣金额
 */
const getCouponDiscount = (merchantId: number) => {
  const selectedCoupon = couponStore.getSelectedCouponForMerchant(merchantId)
  if (!selectedCoupon) {
    console.log(`💰 商家${merchantId}未选择优惠券`)
    return 0
  }

  const discountAmount = selectedCoupon.discount_amount || selectedCoupon.coupon.amount || 0

  console.log(`💰 商家${merchantId}优惠券折扣:`, {
    selectedCoupon,
    discountAmount,
    couponName: selectedCoupon.coupon.name,
    couponAmount: selectedCoupon.coupon.amount,
    actualDiscountAmount: selectedCoupon.discount_amount,
  })

  return discountAmount
}

/**
 * 获取促销活动折扣金额
 */
const getPromotionDiscount = (merchantId: number) => {
  const discountAmount = promotionStore.getPromotionDiscount(merchantId)

  console.log(`🎉 商家${merchantId}促销活动折扣:`, {
    discountAmount,
    selectedPromotion: promotionStore.getSelectedPromotion(merchantId),
  })

  return discountAmount
}

/**
 * 判断商家是否有可用的优惠券
 */
const hasAvailableCoupons = (merchantId: number) => {
  // 获取该商家的选中商品
  const merchantGroup = merchantGroups.value.find((g) => g.merchantId === merchantId)
  const hasSelectedItems = merchantGroup?.items.some((item) => item.selected) || false

  // 如果没有选中商品，不显示优惠券选择器
  if (!hasSelectedItems) {
    return false
  }

  const availableCoupons = couponStore.getAvailableCouponsForMerchant(merchantId)
  const hasAvailable = availableCoupons.length > 0

  console.log(`🎫 商家${merchantId}优惠券可用性检查:`, {
    hasSelectedItems,
    availableCount: availableCoupons.length,
    hasAvailable,
    coupons: availableCoupons.map((c) => ({ id: c.id, name: c.coupon?.name })),
  })

  return hasAvailable
}

/**
 * 判断商家是否有可用的促销活动
 */
const hasAvailablePromotions = (merchantId: number) => {
  // 获取该商家的选中商品
  const merchantGroup = merchantGroups.value.find((g) => g.merchantId === merchantId)
  const hasSelectedItems = merchantGroup?.items.some((item) => item.selected) || false

  // 如果没有选中商品，不显示促销活动选择器
  if (!hasSelectedItems) {
    return false
  }

  // 🔧 修复：同时检查验证过的促销活动和新版API的促销活动
  const applicablePromotions = promotionStore.getApplicablePromotions(merchantId)
  const availablePromotions = applicablePromotions.filter((p) => p.applicable)

  // 🔧 新增：检查新版API获取的促销活动数据
  const merchantPromotions = promotionStore.getMerchantPromotions(merchantId)

  const hasAvailable = availablePromotions.length > 0 || merchantPromotions.length > 0

  console.log(`🎉 商家${merchantId}促销活动可用性检查:`, {
    hasSelectedItems,
    applicableCount: applicablePromotions.length,
    availableCount: availablePromotions.length,
    merchantPromotionsCount: merchantPromotions.length,
    hasAvailable,
    applicablePromotions: availablePromotions.map((p) => ({
      id: p.promotion.id,
      name: p.promotion.name,
    })),
    merchantPromotions: merchantPromotions.map((p) => ({ id: p.id, name: p.name })),
  })

  return hasAvailable
}

/**
 * 计算商家总计
 */
const getMerchantTotal = (group: any) => {
  // 如果商家没有选中商品，总计为0（不包含配送费）
  if (group.selectedSubtotal <= 0) {
    console.log(`🧾 商家${group.merchantId}没有选中商品，总计为0`)
    return 0
  }

  // 根据配送方式计算配送费
  const deliveryMethod = getDeliveryMethod(group.merchantId)
  let deliveryFee = 0

  if (deliveryMethod === 'pickup') {
    deliveryFee = 0 // 自取时配送费为0
  } else {
    const deliveryFeeResult = cartStore.getDeliveryFeeResult(group.merchantId)
    deliveryFee = deliveryFeeResult ? deliveryFeeResult.deliveryFee : 3.0
  }

  const promotionDiscount = calculatePromotionDiscount(group)
  const couponDiscount = getCouponDiscount(group.merchantId)
  const activityDiscount = getPromotionDiscount(group.merchantId) // 新增促销活动折扣

  let total =
    group.selectedSubtotal +
    group.selectedPackagingFee +
    deliveryFee -
    promotionDiscount -
    couponDiscount -
    activityDiscount

  // 🔧 确保总计不会为负数，如果为负数则设为0
  if (total < 0) {
    console.warn(`⚠️ 商家${group.merchantId}计算出负金额，已调整为0:`, {
      originalTotal: total,
      selectedSubtotal: group.selectedSubtotal,
      selectedPackagingFee: group.selectedPackagingFee,
      deliveryMethod,
      deliveryFee,
      promotionDiscount,
      couponDiscount,
      activityDiscount,
    })
    total = 0
  }

  console.log(`🧾 商家${group.merchantId}总计计算:`, {
    merchantId: group.merchantId,
    merchantName: group.merchantName,
    supportPickup: group.supportPickup,
    selectedSubtotal: group.selectedSubtotal,
    selectedPackagingFee: group.selectedPackagingFee,
    deliveryMethod,
    deliveryFee,
    promotionDiscount,
    couponDiscount,
    activityDiscount,
    total,
    hasSelectedItems: group.selectedSubtotal > 0,
    calculation: `${group.selectedSubtotal} + ${group.selectedPackagingFee} + ${deliveryFee} - ${promotionDiscount} - ${couponDiscount} - ${activityDiscount} = ${total}`,
  })

  return total
}

/**
 * 是否显示配送费调试信息
 */
const showDeliveryDebugInfo = (_merchantId: number) => {
  // 在开发环境或者特定条件下显示调试信息
  return import.meta.env.DEV || uni.getStorageSync('showDeliveryDebug') === 'true'
}

/**
 * 获取配送费调试信息
 */
const getDeliveryDebugInfo = (merchantId: number): string[] => {
  const debugInfo: string[] = []
  const deliveryFeeResult = cartStore.getDeliveryFeeResult(merchantId)
  const config = cartStore.deliveryConfig
  const defaultAddress = addressStore.defaultAddress
  const group = merchantGroups.value.find((g) => g.merchantId === merchantId)

  if (!deliveryFeeResult || !config || !group) {
    debugInfo.push('⚠️ 配送费计算数据不完整')
    return debugInfo
  }

  // 基础信息
  debugInfo.push(`📍 商家ID: ${merchantId}`)
  debugInfo.push(`🏪 商家名称: ${group.merchantName}`)
  debugInfo.push(`📦 支持自取: ${group.supportPickup === 1 ? '是' : '否'}`)
  debugInfo.push(
    `🚛 配送方式: ${getDeliveryMethod(merchantId) === 'pickup' ? '到店自取' : '外卖配送'}`,
  )
  debugInfo.push(`💰 订单金额: ¥${group.selectedSubtotal.toFixed(2)}`)
  debugInfo.push(`🚚 配送费: ¥${deliveryFeeResult.deliveryFee.toFixed(2)}`)

  // 距离信息
  if (deliveryFeeResult.distance) {
    debugInfo.push(`📏 配送距离: ${formatDistance(deliveryFeeResult.distance)}`)
  }

  // 地址信息
  if (defaultAddress) {
    const lat = defaultAddress.location_latitude || defaultAddress.locationLatitude
    const lng = defaultAddress.location_longitude || defaultAddress.locationLongitude
    if (lat && lng) {
      debugInfo.push(`🏠 用户坐标: ${lat}, ${lng}`)
    } else {
      debugInfo.push(`🏠 用户地址无坐标信息`)
    }
  } else {
    debugInfo.push(`🏠 未找到默认地址`)
  }

  // 商家坐标信息
  if (group.merchantLatitude && group.merchantLongitude) {
    debugInfo.push(`🏪 商家坐标: ${group.merchantLatitude}, ${group.merchantLongitude}`)
  } else {
    debugInfo.push(`🏪 商家坐标信息缺失`)
  }

  return debugInfo
}

// 页面生命周期
onLoad(() => {
  console.log('购物车页面加载')
})

/**
 * 初始化配送方式
 */
const initializeDeliveryMethods = () => {
  console.log('🚚 初始化配送方式开始')

  merchantGroups.value.forEach((group) => {
    // 如果商家支持自取且还没有设置配送方式，则设置默认为配送
    if (group.supportPickup === 1 && !deliveryMethods.value[group.merchantId]) {
      deliveryMethods.value[group.merchantId] = 'delivery'
      console.log(
        `🚚 商家${group.merchantId}(${group.merchantName})支持自取，初始化配送方式为: 配送`,
      )
    } else if (group.supportPickup === 1) {
      console.log(
        `🚚 商家${group.merchantId}(${group.merchantName})支持自取，当前配送方式: ${deliveryMethods.value[group.merchantId] === 'pickup' ? '自取' : '配送'}`,
      )
    } else {
      console.log(`🚚 商家${group.merchantId}(${group.merchantName})不支持自取，仅支持配送`)
    }
  })

  console.log('🚚 初始化配送方式完成，当前配送方式状态:', deliveryMethods.value)
}

/**
 * 格式化规格信息
 */
const formatSpecs = (specs: Record<string, string>) => {
  return Object.entries(specs)
    .map(([key, value]) => `${key}: ${value}`)
    .join(', ')
}

/**
 * 复制配送费调试信息
 */
const copyDeliveryDebugInfo = (merchantId: number) => {
  try {
    const debugInfo = getDeliveryDebugInfo(merchantId)
    const debugText = [
      '🚚 配送费调试信息',
      '='.repeat(30),
      ...debugInfo,
      '='.repeat(30),
      `复制时间: ${new Date().toLocaleString()}`,
    ].join('\n')

    // 使用uni-app的复制到剪贴板API
    uni.setClipboardData({
      data: debugText,
      success: () => {
        uni.showToast({
          title: '调试信息已复制',
          icon: 'success',
          duration: 2000,
        })
        console.log('📋 配送费调试信息已复制到剪贴板:', debugText)
      },
      fail: (error) => {
        console.error('📋 复制失败:', error)
        uni.showToast({
          title: '复制失败',
          icon: 'error',
          duration: 2000,
        })
      },
    })
  } catch (error) {
    console.error('📋 复制调试信息失败:', error)
    uni.showToast({
      title: '复制失败',
      icon: 'error',
      duration: 2000,
    })
  }
}

/**
 * 跳转到首页
 */
const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

/**
 * 获取商家的配送方式
 */
const getDeliveryMethod = (merchantId: number): 'delivery' | 'pickup' => {
  return deliveryMethods.value[merchantId] || 'delivery'
}

/**
 * 设置商家的配送方式
 */
const setDeliveryMethod = (merchantId: number, method: 'delivery' | 'pickup') => {
  deliveryMethods.value[merchantId] = method

  // 查找商家分组
  const group = merchantGroups.value.find((g) => g.merchantId === merchantId)

  // 配送方式改变时，重新计算配送费
  if (method === 'pickup') {
    // 自取时配送费为0，更新商家分组中的配送费
    if (group) {
      group.deliveryFee = 0
    }
  } else {
    // 配送时重新计算配送费
    cartStore.calculateAllDeliveryFees()
  }

  console.log(`🚚 商家${merchantId}配送方式已设置为: ${method === 'delivery' ? '配送' : '自取'}`, {
    merchantId,
    merchantName: group?.merchantName,
    supportPickup: group?.supportPickup,
    method,
    newDeliveryFee: method === 'pickup' ? 0 : group?.deliveryFee,
  })
}

/**
 * 格式化指定配送方式的配送费显示
 */
const formatDeliveryFeeForMethod = (merchantId: number, method: 'delivery' | 'pickup'): string => {
  if (method === 'pickup') {
    return '0.00'
  }

  const result = cartStore.getDeliveryFeeResult(merchantId)
  if (!result) {
    return '3.00' // 默认配送费
  }

  return result.deliveryFee.toFixed(2)
}

// 支付方式相关状态
const showPaymentPopup = ref(false)
const isCreatingOrder = ref(false)

// 配送方式状态管理
const deliveryMethods = ref<Record<number, 'delivery' | 'pickup'>>({})

// 支付方式列表
const paymentMethods = ref([
  {
    code: 'wechat',
    name: '微信支付',
    description: '推荐移动端用户使用',
    icon: '/static/images/payment/wechat.png',
  },
  {
    code: 'alipay',
    name: '支付宝',
    description: '适合所有平台用户',
    icon: '/static/images/payment/alipay.png',
  },
  {
    code: 'balance',
    name: '余额支付',
    description: '使用账户余额支付',
    icon: '/static/images/payment/balance.png',
  },
])

// 计算总金额（不含配送费）
const totalAmount = computed(() => {
  return merchantGroups.value.reduce((total, group) => {
    return total + group.selectedSubtotal
  }, 0)
})

/**
 * 结算
 */
const handleCheckout = async () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请选择要结算的商品',
      icon: 'none',
    })
    return
  }

  // 检查是否选择了收货地址
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择收货地址',
      icon: 'none',
    })
    return
  }

  try {
    // 获取选中的商品
    const selectedItems = cartList.value.filter((item) => item.selected)

    // 确保takeoutStore中的购物车数据是最新的
    await takeoutStore.getCart()

    // 同步选中状态到takeoutStore
    // 首先取消所有选中状态
    const allCartItemIds = takeoutStore.cart.items.map((item) => item.cart_item_id)
    if (allCartItemIds.length > 0) {
      await takeoutStore.selectCartItems(allCartItemIds, false)
    }

    // 然后选中对应的商品
    const selectedCartItemIds = selectedItems
      .map((item) => {
        // 根据productId找到对应的takeout cart item
        const takeoutItem = takeoutStore.cart.items.find(
          (tItem) => tItem.food_id === item.productId,
        )
        return takeoutItem?.cart_item_id
      })
      .filter((id) => id !== undefined) as number[]

    if (selectedCartItemIds.length > 0) {
      await takeoutStore.selectCartItems(selectedCartItemIds, true)
    }

    // 显示支付方式选择弹窗
    showPaymentPopup.value = true
  } catch (error) {
    console.error('结算失败:', error)
    uni.showToast({
      title: '结算失败，请重试',
      icon: 'error',
    })
  }
}

// 定义商家订单接口
interface MerchantOrderRequest {
  merchantID: number
  cartItemIDs: number[]
  couponID?: number
  promotionID?: number
  deliveryTime?: string
  remark?: string
  deliveryType?: number // 配送方式：0-配送订单，2-自提订单
}

// 定义新版多商家订单请求接口
interface CreateMultiMerchantOrderRequest {
  takeoutAddressID: number
  paymentMethod: string
  merchantOrders: MerchantOrderRequest[]
}

/**
 * 处理支付方式确认
 */
const handlePaymentConfirm = async (paymentMethod: string) => {
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择收货地址',
      icon: 'none',
    })
    return
  }

  isCreatingOrder.value = true

  try {
    // 构建按商家分组的订单数据
    const merchantOrders: MerchantOrderRequest[] = []

    for (const group of merchantGroups.value) {
      // 获取该商家选中的商品
      const selectedItems = group.items.filter((item) => item.selected)

      if (selectedItems.length === 0) {
        continue // 跳过没有选中商品的商家
      }

      // 获取该商家选中商品的cart_item_id
      const cartItemIDs = selectedItems
        .map((item) => {
          const takeoutItem = takeoutStore.cart.items.find(
            (tItem) => tItem.food_id === item.productId,
          )
          return takeoutItem?.cart_item_id
        })
        .filter((id) => id !== undefined) as number[]

      if (cartItemIDs.length === 0) {
        console.warn(`商家 ${group.merchantId} 没有找到有效的购物车项`)
        continue
      }

      // 构建商家订单数据
      const deliveryMethod = getDeliveryMethod(group.merchantId)
      const deliveryType = deliveryMethod === 'pickup' ? 2 : 0
      const merchantOrder: MerchantOrderRequest = {
        merchantID: group.merchantId,
        cartItemIDs: cartItemIDs,
        remark: group.remark || '', // 使用商家备注
        deliveryType: deliveryType, // 配送方式：0-配送订单，2-自提订单
      }

      console.log(`📦 商家 ${group.merchantId} 订单配送信息:`, {
        deliveryMethod: deliveryMethod,
        deliveryType: deliveryType,
        deliveryTypeText: deliveryType === 2 ? '自提订单' : '配送订单',
        merchantName: group.merchantName,
        supportPickup: group.supportPickup,
      })

      // 从couponStore获取该商家选中的优惠券ID
      const selectedCoupon = couponStore.getSelectedCouponForMerchant(group.merchantId)
      if (selectedCoupon) {
        merchantOrder.couponID = selectedCoupon.id || selectedCoupon.coupon?.id
        console.log(`🎫 商家 ${group.merchantId} 选中优惠券:`, {
          couponId: merchantOrder.couponID,
          couponName: selectedCoupon.coupon?.name,
        })
      }

      // 从promotionStore获取该商家选中的促销活动ID
      const selectedPromotion = promotionStore.getSelectedPromotion(group.merchantId)
      if (selectedPromotion) {
        merchantOrder.promotionID = selectedPromotion.id
        console.log(`🎉 商家 ${group.merchantId} 选中促销活动:`, {
          promotionId: merchantOrder.promotionID,
          promotionName: selectedPromotion.name,
        })
      }

      merchantOrders.push(merchantOrder)
    }

    if (merchantOrders.length === 0) {
      throw new Error('没有找到有效的商家订单')
    }

    // 构建新版多商家订单参数
    const orderParams: CreateMultiMerchantOrderRequest = {
      takeoutAddressID: selectedAddress.value.id,
      paymentMethod: paymentMethod,
      merchantOrders: merchantOrders,
    }

    console.log('创建订单参数 (新版多商家格式):', orderParams)

    // 调用创建订单API - 使用类型断言来兼容现有API
    const orderResult = await createTakeoutOrder(orderParams as any)

    console.log('订单创建成功:', orderResult)

    // 关闭支付弹窗
    showPaymentPopup.value = false

    // 显示成功提示
    uni.showToast({
      title: '订单创建成功',
      icon: 'success',
    })

    // 刷新购物车
    await cartStore.fetchCartList()

    // 支付成功后跳转到订单列表页面
    uni.navigateTo({
      url: '/pages/order/list',
    })
  } catch (error) {
    console.error('创建订单失败:', error)
    uni.showToast({
      title: error.message || '创建订单失败，请重试',
      icon: 'error',
    })
  } finally {
    isCreatingOrder.value = false
  }
}

/**
 * 处理支付弹窗关闭
 */
const handlePaymentClose = () => {
  showPaymentPopup.value = false
}

/**
 * 🔧 验证优惠券可用性
 */
const validateCouponAvailability = (couponData: any): boolean => {
  try {
    const coupon = couponData.coupon
    const now = new Date()

    // 1. 检查优惠券状态
    if (coupon.status !== 1) {
      console.log(`🚫 优惠券状态无效: ${coupon.name}, 状态: ${coupon.status_text}`)
      return false
    }

    // 2. 检查用户优惠券状态
    if (couponData.status !== 1) {
      console.log(`🚫 用户优惠券状态无效: ${coupon.name}, 状态: ${couponData.status_text}`)
      return false
    }

    // 3. 检查优惠券是否已使用
    if (couponData.used_time && couponData.used_time !== '0001-01-01T00:00:00Z') {
      console.log(`🚫 优惠券已使用: ${coupon.name}, 使用时间: ${couponData.used_time}`)
      return false
    }

    // 4. 检查优惠券开始时间
    const startTime = new Date(coupon.start_time)
    if (now < startTime) {
      console.log(`🚫 优惠券未开始: ${coupon.name}, 开始时间: ${coupon.start_time}`)
      return false
    }

    // 5. 检查优惠券结束时间
    const endTime = new Date(coupon.end_time)
    if (now > endTime) {
      console.log(`🚫 优惠券已过期: ${coupon.name}, 结束时间: ${coupon.end_time}`)
      return false
    }

    // 6. 检查优惠券发行数量限制
    if (coupon.total_limit > 0 && coupon.issued_count >= coupon.total_limit) {
      console.log(
        `🚫 优惠券已达发行上限: ${coupon.name}, 已发行: ${coupon.issued_count}/${coupon.total_limit}`,
      )
      return false
    }

    console.log(`✅ 优惠券有效: ${coupon.name}`, {
      startTime: coupon.start_time,
      endTime: coupon.end_time,
      status: coupon.status_text,
      userStatus: couponData.status_text,
    })

    return true
  } catch (error) {
    console.error('❌ 验证优惠券可用性失败:', error)
    return false
  }
}

/**
 * 🔧 处理新版API返回的优惠券数据
 */
const processNewApiCouponData = async () => {
  try {
    console.log('🔧 处理新版API返回的优惠券数据')

    // 获取所有商家ID
    const merchantIds = Array.from(new Set(cartList.value.map((item) => item.merchantId)))

    if (merchantIds.length === 0) {
      console.log('🔧 没有商家需要处理优惠券数据')
      return
    }

    // 从促销活动store中获取优惠券数据并设置到优惠券store
    for (const merchantId of merchantIds) {
      const merchantCoupons = promotionStore.getMerchantCoupons(merchantId)

      if (merchantCoupons.length > 0) {
        console.log(`🔧 为商家 ${merchantId} 处理优惠券数据:`, merchantCoupons.length)

        // 🔧 过滤并转换优惠券数据，排除已过期的优惠券
        const formattedCoupons = merchantCoupons
          .filter((couponData: any) => {
            // 检查优惠券是否有效
            const isValid = validateCouponAvailability(couponData)
            if (!isValid) {
              console.log(`🚫 过滤已过期优惠券: ${couponData.coupon.name}`, {
                endTime: couponData.coupon.end_time,
                status: couponData.coupon.status_text,
              })
            }
            return isValid
          })
          .map((couponData: any) => ({
            ...couponData,
            can_use: true, // 经过过滤的都是可用的
            discount_amount: couponData.coupon.amount, // 设置折扣金额
          }))

        // 设置到优惠券store的availableCoupons中
        couponStore.availableCoupons[merchantId] = formattedCoupons

        console.log(`✅ 商家 ${merchantId} 优惠券数据处理完成:`, formattedCoupons.length)
      } else {
        console.log(`🔧 商家 ${merchantId} 没有优惠券数据`)
      }
    }
  } catch (error) {
    console.error('❌ 处理新版API优惠券数据失败:', error)
  }
}

/**
 * 初始化优惠券数据
 */
const initializeCoupons = async () => {
  try {
    console.log('🎫 初始化购物车优惠券数据')

    // 🔧 首先处理新版API返回的优惠券数据
    await processNewApiCouponData()

    // 检查是否已有我的优惠券数据，避免重复加载
    if (couponStore.myCoupons.length === 0) {
      console.log('🎫 首次加载用户优惠券列表')
      await couponStore.fetchMyCoupons({ refresh: true })
    } else {
      console.log('🎫 使用已缓存的用户优惠券数据，数量:', couponStore.myCoupons.length)
    }

    // 为每个商家加载可用优惠券（如果新版API没有提供数据）
    for (const group of merchantGroups.value) {
      if (group.items.some((item) => item.selected)) {
        const selectedAmount = calculateMerchantSelectedAmount(group)
        const selectedFoodIds = group.items
          .filter((item) => item.selected)
          .map((item) => item.productId)
          .join(',')

        if (selectedAmount > 0 && selectedFoodIds) {
          // 检查是否已有该商家的优惠券数据
          const existingAvailable = couponStore.getAvailableCouponsForMerchant(group.merchantId)
          const existingUnavailable = couponStore.getUnavailableCouponsForMerchant(group.merchantId)

          if (existingAvailable.length === 0 && existingUnavailable.length === 0) {
            console.log(`🎫 为商家 ${group.merchantId} 加载优惠券`, {
              amount: selectedAmount,
              foodIds: selectedFoodIds,
            })

            await couponStore.fetchAvailableCouponsForOrder({
              merchant_id: group.merchantId,
              total_amount: selectedAmount,
              food_ids: selectedFoodIds,
            })
          } else {
            console.log(`🎫 商家 ${group.merchantId} 已有优惠券数据，跳过加载`, {
              available: existingAvailable.length,
              unavailable: existingUnavailable.length,
            })
          }
        }
      }
    }

    console.log('✅ 购物车优惠券数据初始化完成')
  } catch (error) {
    console.error('❌ 初始化优惠券数据失败:', error)
  }
}

/**
 * 初始化促销活动数据
 */
const initializePromotions = async () => {
  try {
    console.log('🎉 初始化购物车促销活动数据')

    // 收集所有有选中商品的商家ID
    const merchantIds = merchantGroups.value
      .filter((group) => group.items.some((item) => item.selected))
      .map((group) => group.merchantId)

    if (merchantIds.length === 0) {
      console.log('🎉 没有选中商品，跳过促销活动加载')
      return
    }

    console.log('🎉 批量加载商家促销活动:', { merchantIds })

    // 使用新版API批量获取所有商家的促销活动和优惠券信息
    await promotionStore.fetchMerchantsPromotionsAndCoupons(merchantIds)

    console.log('✅ 购物车促销活动数据初始化完成')
  } catch (error) {
    console.error('❌ 初始化促销活动数据失败:', error)
  }
}

/**
 * 重新验证指定商家的促销活动和优惠券
 */
const revalidateMerchantOffers = async (merchantId: number) => {
  try {
    console.log(`🔄 重新验证商家 ${merchantId} 的促销活动和优惠券`)

    const group = merchantGroups.value.find((g) => g.merchantId === merchantId)
    if (!group) {
      console.log(`❌ 未找到商家 ${merchantId} 的分组信息`)
      return
    }

    const selectedItems = group.items.filter((item) => item.selected)
    const selectedAmount = calculateMerchantSelectedAmount(group)
    const selectedFoodIds = selectedItems.map((item) => item.productId).join(',')

    console.log(`🔄 商家 ${merchantId} 当前状态:`, {
      selectedItemsCount: selectedItems.length,
      selectedAmount,
      selectedFoodIds,
    })

    // 如果没有选中商品，清除该商家的促销活动和优惠券选择
    if (selectedItems.length === 0 || selectedAmount <= 0) {
      console.log(`🔄 商家 ${merchantId} 没有选中商品，清除促销活动和优惠券选择`)
      promotionStore.clearSelectedPromotion(merchantId)
      couponStore.clearSelectedCoupon(merchantId)
      // 清除最优优惠组合缓存
      delete bestOfferCombinations.value[merchantId]
      return
    }

    // 重新验证促销活动
    await promotionStore.validatePromotionsForOrder({
      merchant_id: merchantId,
      total_amount: selectedAmount,
      food_ids: selectedFoodIds,
    })

    // 重新验证优惠券
    await couponStore.fetchAvailableCouponsForOrder({
      merchant_id: merchantId,
      total_amount: selectedAmount,
      food_ids: selectedFoodIds,
    })

    // 自动选择最优优惠组合
    await autoSelectBestOffers(merchantId)

    console.log(`✅ 商家 ${merchantId} 促销活动和优惠券验证完成，已自动选择最优优惠`)
  } catch (error) {
    console.error(`❌ 重新验证商家 ${merchantId} 的促销活动和优惠券失败:`, error)
  }
}

/**
 * 🧠 智能优惠算法 - 计算最优优惠组合
 */
const calculateBestOfferCombination = async (
  merchantId: number,
): Promise<BestOfferCombination | null> => {
  try {
    console.log(`🧠 开始计算商家 ${merchantId} 的最优优惠组合`)

    const group = merchantGroups.value.find((g) => g.merchantId === merchantId)
    if (!group || group.selectedSubtotal <= 0) {
      console.log(`❌ 商家 ${merchantId} 没有选中商品，跳过计算`)
      return null
    }

    // 获取可用的优惠券和促销活动
    // 1. 从新版API获取的优惠券数据（来自 /api/v1/user/takeout/merchants/promotions-coupons）
    const availableCoupons = couponStore.getAvailableCouponsForMerchant(merchantId)

    // 2. 从新版API获取的促销活动数据
    const merchantPromotions = promotionStore.getMerchantPromotions(merchantId)

    // 3. 如果没有通过验证API获取适用性，则使用本地验证
    let availablePromotions = promotionStore
      .getApplicablePromotions(merchantId)
      .filter((p) => p.applicable)

    // 如果没有适用性数据，则对所有促销活动进行本地验证
    if (availablePromotions.length === 0 && merchantPromotions.length > 0) {
      console.log(`🧠 对商家 ${merchantId} 的促销活动进行本地验证`)

      // 为促销活动创建简单的验证结果
      availablePromotions = merchantPromotions
        .map((promotion) => {
          // 简单的验证逻辑：检查最低消费金额
          const rules = promotion.rules
          let applicable = true
          let discountAmount = 0

          if (rules && typeof rules === 'object' && rules.coupon) {
            const minAmount = rules.coupon.min_order_amount || 0
            const amount = rules.coupon.amount || 0

            applicable = group.selectedSubtotal >= minAmount
            discountAmount = applicable ? amount : 0
          }

          return {
            promotion,
            applicable,
            discount_amount: discountAmount,
            final_amount: group.selectedSubtotal - discountAmount, // 添加缺少的属性
            reason: applicable ? '满足使用条件' : '未达到最低消费金额',
          }
        })
        .filter((result) => result.applicable)
    }

    console.log(`🧠 商家 ${merchantId} 可用优惠:`, {
      couponsCount: availableCoupons.length,
      promotionsCount: availablePromotions.length,
      merchantPromotionsCount: merchantPromotions.length,
      selectedAmount: group.selectedSubtotal,
    })

    if (availableCoupons.length === 0 && availablePromotions.length === 0) {
      console.log(`❌ 商家 ${merchantId} 没有可用的优惠，跳过计算`)
      return null
    }

    let bestCombination: BestOfferCombination = {
      merchantId,
      bestCoupon: null,
      bestPromotion: null,
      totalSavings: 0,
      calculatedAt: Date.now(),
    }

    // 计算所有可能的组合
    const combinations = []

    // 1. 只使用优惠券的组合
    for (const coupon of availableCoupons) {
      // 🔧 验证优惠券是否满足使用条件
      if (!coupon.can_use) {
        console.log(`🚫 跳过不可用优惠券: ${coupon.coupon.name}`)
        continue
      }

      // 🔧 检查最低消费金额
      const minOrderAmount = coupon.coupon.min_order_amount || 0
      if (group.selectedSubtotal < minOrderAmount) {
        console.log(
          `🚫 跳过未达最低消费优惠券: ${coupon.coupon.name}, 需要¥${minOrderAmount}, 当前¥${group.selectedSubtotal}`,
        )
        continue
      }

      // 🔧 再次验证优惠券有效性（防止过期优惠券）
      if (!validateCouponAvailability(coupon)) {
        console.log(`🚫 跳过无效优惠券: ${coupon.coupon.name}`)
        continue
      }

      // 🔧 检查使用该优惠券是否会导致合计金额为负数
      const couponDiscount = coupon.discount_amount || 0
      if (checkNegativeAmount(merchantId, couponDiscount, 0)) {
        console.log(
          `🚫 跳过会导致负金额的优惠券: ${coupon.coupon.name}, 折扣金额: ¥${couponDiscount}`,
        )
        continue
      }

      combinations.push({
        coupon,
        promotion: null,
        savings: couponDiscount,
      })
    }

    // 2. 只使用促销活动的组合
    for (const promotion of availablePromotions) {
      // 处理不同的促销活动数据结构
      const discountAmount = promotion.discount_amount || 0

      // 🔧 检查使用该促销活动是否会导致合计金额为负数
      if (checkNegativeAmount(merchantId, 0, discountAmount)) {
        console.log(
          `🚫 跳过会导致负金额的促销活动: ${promotion.promotion?.name}, 折扣金额: ¥${discountAmount}`,
        )
        continue
      }

      combinations.push({
        coupon: null,
        promotion,
        savings: discountAmount,
      })
    }

    // 3. 优惠券 + 促销活动的组合（如果可以叠加）
    for (const coupon of availableCoupons) {
      // 🔧 再次验证优惠券有效性
      if (!coupon.can_use || !validateCouponAvailability(coupon)) {
        continue
      }

      // 🔧 检查优惠券最低消费金额
      const minOrderAmount = coupon.coupon.min_order_amount || 0
      if (group.selectedSubtotal < minOrderAmount) {
        continue
      }

      for (const promotion of availablePromotions) {
        // 检查是否可以叠加使用
        if (canCombineOffers(coupon, promotion)) {
          const couponDiscount = coupon.discount_amount || 0
          const promotionDiscount = promotion.discount_amount || 0
          const combinedSavings = couponDiscount + promotionDiscount

          // 🔧 检查叠加使用是否会导致合计金额为负数
          if (checkNegativeAmount(merchantId, couponDiscount, promotionDiscount)) {
            console.log(
              `🚫 跳过会导致负金额的组合: ${coupon.coupon.name} + ${promotion.promotion?.name}, 总折扣: ¥${combinedSavings}`,
            )
            continue
          }

          combinations.push({
            coupon,
            promotion,
            savings: combinedSavings,
          })
        }
      }
    }

    // 找出最优组合
    let maxSavings = 0
    for (const combination of combinations) {
      if (combination.savings > maxSavings) {
        maxSavings = combination.savings
        bestCombination.bestCoupon = combination.coupon
        bestCombination.bestPromotion = combination.promotion
        bestCombination.totalSavings = combination.savings
      }
    }

    console.log(`🧠 商家 ${merchantId} 最优优惠组合计算完成:`, {
      bestCoupon: bestCombination.bestCoupon?.coupon?.name,
      bestPromotion: bestCombination.bestPromotion?.promotion?.name,
      totalSavings: bestCombination.totalSavings,
      combinationsCount: combinations.length,
    })

    // 缓存结果
    bestOfferCombinations.value[merchantId] = bestCombination

    return bestCombination
  } catch (error) {
    console.error(`❌ 计算商家 ${merchantId} 最优优惠组合失败:`, error)
    return null
  }
}

/**
 * 检查优惠券和促销活动是否可以叠加使用
 */
const canCombineOffers = (coupon: any, promotion: any): boolean => {
  // 基本规则：大部分优惠券和促销活动可以叠加
  // 但需要检查特殊限制条件

  // 1. 检查优惠券是否允许与促销活动叠加
  if (coupon.coupon?.cannot_combine_with_promotion) {
    return false
  }

  // 2. 检查促销活动是否允许与优惠券叠加
  if (promotion.promotion?.cannot_combine_with_coupon) {
    return false
  }

  // 3. 检查是否是互斥的优惠类型
  if (coupon.coupon?.exclusive || promotion.promotion?.exclusive) {
    return false
  }

  // 默认允许叠加
  return true
}

/**
 * 🧠 智能优惠算法 - 自动选择最优优惠组合
 */
const autoSelectBestOffers = async (merchantId: number) => {
  try {
    console.log(`🧠 开始为商家 ${merchantId} 自动选择最优优惠`)

    // 计算最优组合
    const bestCombination = await calculateBestOfferCombination(merchantId)

    if (!bestCombination || bestCombination.totalSavings <= 0) {
      console.log(`❌ 商家 ${merchantId} 暂无可用优惠`)
      return false
    }

    // 应用最优组合
    if (bestCombination.bestCoupon) {
      console.log(`🎫 自动选择优惠券: ${bestCombination.bestCoupon.coupon.name}`)
      couponStore.selectCouponForMerchant(merchantId, bestCombination.bestCoupon)
    }

    if (bestCombination.bestPromotion) {
      console.log(`🎉 自动选择促销活动: ${bestCombination.bestPromotion.promotion.name}`)

      // 🔧 修复：同时设置选中的促销活动和对应的折扣金额
      promotionStore.selectPromotion(merchantId, bestCombination.bestPromotion.promotion)

      // 🔧 确保折扣金额被正确设置到applicablePromotions中
      if (!promotionStore.applicablePromotions[merchantId]) {
        promotionStore.applicablePromotions[merchantId] = []
      }

      // 检查是否已存在该促销活动的验证结果
      const existingResult = promotionStore.applicablePromotions[merchantId].find(
        (p) => p.promotion.id === bestCombination.bestPromotion.promotion.id,
      )

      if (!existingResult) {
        // 添加验证结果
        promotionStore.applicablePromotions[merchantId].push({
          promotion: bestCombination.bestPromotion.promotion,
          applicable: true,
          discount_amount: bestCombination.bestPromotion.discount_amount,
          final_amount: 0, // 这里不需要计算，会在其他地方计算
          reason: '智能优惠算法自动选择',
        })

        console.log(`🔧 为促销活动设置折扣金额: ${bestCombination.bestPromotion.discount_amount}`)
      }
    }

    console.log(
      `✅ 商家 ${merchantId} 最优优惠自动选择完成，可省¥${bestCombination.totalSavings.toFixed(2)}`,
    )
    return true
  } catch (error) {
    console.error(`❌ 自动选择最优优惠失败:`, error)
    return false
  }
}

/**
 * 🤖 为所有商家自动选择最优优惠
 */
const autoSelectBestOffersForAllMerchants = async () => {
  console.log('🤖 开始为所有商家自动选择最优优惠')

  const promises = merchantGroups.value
    .filter((group) => group.selectedSubtotal > 0)
    .map((group) => autoSelectBestOffers(group.merchantId))

  const results = await Promise.allSettled(promises)

  const successCount = results.filter(
    (result) => result.status === 'fulfilled' && result.value,
  ).length
  const totalCount = results.length

  console.log(`🤖 自动选择优惠完成: ${successCount}/${totalCount} 个商家成功选择最优优惠`)
}

onShow(async () => {
  console.log('🛒 [Cart Page] 购物车页面显示，强制刷新数据')
  // 强制刷新购物车数据，不使用缓存
  await cartStore.fetchCartList(true)
  await addressStore.fetchAddressList()

  // 初始化配送方式
  initializeDeliveryMethods()

  // 等待购物车数据加载完成后再初始化优惠券和促销活动
  if (!isEmpty.value) {
    await Promise.all([initializeCoupons(), initializePromotions()])

    // 自动为所有有选中商品的商家选择最优优惠
    await autoSelectBestOffersForAllMerchants()
  }
})

onMounted(() => {
  console.log('购物车页面挂载完成')

  // 注册购物车数据更新事件监听器
  console.log('🎧 [Cart Page] 注册购物车数据更新事件监听器')
  uni.$on('cartDataUpdated', handleCartDataUpdated)
  uni.$on('takeoutCartUpdated', handleTakeoutCartUpdated)
})

// 监听购物车数据更新事件
const handleCartDataUpdated = async (eventData: any) => {
  console.log('🎯 [Cart Page] 收到购物车数据更新事件:', eventData)

  // 强制刷新购物车数据
  await cartStore.fetchCartList(true)

  console.log('✅ [Cart Page] 购物车数据事件刷新完成')
}

// 监听takeout购物车数据更新事件
const handleTakeoutCartUpdated = async (eventData: any) => {
  console.log('🎯 [Cart Page] 收到takeout购物车数据更新事件:', eventData)

  // 强制刷新购物车数据
  await cartStore.fetchCartList(true)

  console.log('✅ [Cart Page] takeout购物车数据事件刷新完成')
}

// 页面销毁时移除事件监听器
onUnmounted(() => {
  console.log('🧹 [Cart Page] 页面销毁，移除事件监听器')
  uni.$off('cartDataUpdated', handleCartDataUpdated)
  uni.$off('takeoutCartUpdated', handleTakeoutCartUpdated)
})
</script>

<style lang="scss" scoped>
.cart-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

// 商家信息
.merchant-info {
  display: flex;
  align-items: center;
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;

  .merchant-logo {
    width: 100rpx;
    height: 100rpx;
    margin-right: 24rpx;
    border-radius: 8rpx;
  }

  .merchant-details {
    flex: 1;

    .merchant-name {
      margin-bottom: 10rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .merchant-delivery {
      font-size: 24rpx;
      color: #666;

      .divider {
        margin: 0 10rpx;
        color: #ddd;
      }
    }
  }

  .merchant-status {
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    border-radius: 8rpx;

    .open {
      color: #52c41a;
      background-color: #f6ffed;
    }

    .closed {
      color: #ff4d4f;
      background-color: #fff2f0;
    }
  }
}

// 优惠券和配送选项
.coupon-section,
.delivery-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;

  .coupon-info,
  .delivery-info {
    display: flex;
    align-items: center;

    .coupon-text {
      margin-left: 16rpx;
      font-size: 28rpx;
      color: #333;
    }

    .delivery-details {
      margin-left: 16rpx;

      .delivery-title {
        display: block;
        font-size: 28rpx;
        color: #333;
      }

      .delivery-desc {
        display: block;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

// 备注区域
.remark-section {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;

  .remark-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    text {
      margin-left: 16rpx;
      font-size: 28rpx;
      color: #333;
    }
  }

  .remark-input {
    width: 100%;
    min-height: 120rpx;
    padding: 16rpx;
    font-size: 28rpx;
    color: #333;
    background-color: #f8f8f8;
    border-radius: 8rpx;
    border: none;
    resize: none;
  }
}

// 全选栏
.select-all-bar {
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.cart-count {
  font-size: 28rpx;
  color: #666;
}

// 购物车列表
.cart-list {
  background-color: #fff;
}

.cart-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
  position: relative;

  &.disabled {
    opacity: 0.6;
  }

  &:last-child {
    border-bottom: none;
  }
}

.item-checkbox {
  margin-right: 20rpx;
  margin-top: 10rpx;
}

.item-content {
  flex: 1;
  display: flex;
  gap: 20rpx;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.item-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.item-combos {
  margin-bottom: 8rpx;
}

.combo-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.combo-name {
  color: #999;
  margin-right: 8rpx;
}

.combo-options {
  color: #666;
}

.item-remark {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  padding: 8rpx 12rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.remark-label {
  color: #999;
  margin-right: 8rpx;
}

.remark-text {
  color: #666;
}

.item-packaging {
  font-size: 24rpx;
  color: #ff6b35;
  margin-bottom: 12rpx;
}

.packaging-label {
  margin-right: 8rpx;
}

.packaging-fee {
  font-weight: 500;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.item-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.current-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
  overflow: hidden;

  &.disabled {
    opacity: 0.5;
  }
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  transition: background-color 0.2s;

  &:active:not(.disabled) {
    background-color: #e8e8e8;
  }

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  &.decrease {
    border-right: 1px solid #e8e8e8;
  }

  &.increase {
    border-left: 1px solid #e8e8e8;
  }
}

.quantity-input {
  min-width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.quantity-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.item-delete {
  margin-left: 20rpx;
  margin-top: 10rpx;
  padding: 10rpx;
}

.unavailable-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.unavailable-text {
  background-color: #ff6b35;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

// 商家分组
.merchant-groups {
  margin-bottom: 120rpx;
}

.merchant-group {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.merchant-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.merchant-stats {
  font-size: 24rpx;
  color: #666;
}

.divider {
  margin: 0 16rpx;
}

.delivery-fee {
  color: #ff5500;
}

.merchant-status {
  .status-text {
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;

    &.open {
      background-color: #e8f5e8;
      color: #52c41a;
    }

    &.closed {
      background-color: #fff2e8;
      color: #fa8c16;
    }
  }
}

// 配送方式选择样式
.delivery-method-section {
  background: #fff;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;

  .delivery-method-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .delivery-method-options {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }

  .delivery-method-option {
    display: flex;
    align-items: center;
    padding: 24rpx;
    border: 1px solid #e8e8e8;
    border-radius: 16rpx;
    background: #fafafa;
    transition: all 0.3s ease;

    &.active {
      border-color: #ff5500;
      background: #fff7f0;
    }

    .option-icon {
      font-size: 40rpx;
      margin-right: 24rpx;
    }

    .option-content {
      flex: 1;

      .option-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 4rpx;
      }

      .option-desc {
        font-size: 24rpx;
        color: #666;
      }
    }

    .option-radio {
      margin-left: 16rpx;
    }
  }
}

.merchant-promotions {
  padding: 20rpx 30rpx;
  background-color: #fff7e6;
  border-bottom: 1px solid #f5f5f5;
}

.coupon-section {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1px solid #f5f5f5;
}

.promotion-section {
  padding: 20rpx 30rpx;
  background-color: #fff5f0;
  border-bottom: 1px solid #f5f5f5;
}

.promotion-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.promotion-tag {
  background-color: #ff5500;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.promotion-text {
  font-size: 24rpx;
  color: #fa8c16;
}

.merchant-remark {
  padding: 30rpx;
  border-top: 1px solid #f5f5f5;
}

.remark-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  text {
    margin-left: 8rpx;
    font-size: 28rpx;
    color: #333;
  }
}

.remark-input {
  width: 100%;
  min-height: 80rpx;
  padding: 20rpx;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
}

.merchant-summary {
  padding: 30rpx;
  border-top: 1px solid #f5f5f5;
  background-color: #fafafa;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &.total {
    font-weight: bold;
    font-size: 32rpx;
    color: #333;
    border-top: 1px solid #e8e8e8;
    padding-top: 16rpx;
    margin-top: 16rpx;
  }
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-value {
  font-size: 28rpx;
  color: #ff5500;

  &.free-delivery {
    color: #52c41a;
    font-weight: bold;
  }

  &.discount {
    color: #52c41a;
    font-weight: bold;
  }
}

.delivery-tip {
  padding: 16rpx 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #fa8c16;
  background-color: #fff7e6;
  padding: 8rpx 12rpx;
  border-radius: 4rpx;
  border-left: 3px solid #fa8c16;
}

.delivery-debug {
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f6f6f6;
  border-radius: 8rpx;
  border: 1px solid #e0e0e0;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.debug-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.debug-actions {
  display: flex;
  align-items: center;
}

.copy-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6rpx;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f5f5;
    border-color: #40a9ff;
  }

  &:active {
    transform: scale(0.95);
  }
}

.copy-text {
  font-size: 22rpx;
  color: #666;
  margin-left: 6rpx;
}

.debug-content {
  margin-top: 8rpx;
}

.debug-item {
  margin-bottom: 8rpx;
}

.debug-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

// 空购物车
.empty-cart {
  padding: 200rpx 40rpx;
}

// 底部结算栏
.bottom-bar {
  position: fixed;
  bottom: 50px; /* 上移50px避免遮挡tabbar */
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.bar-left {
  display: flex;
  align-items: center;
}

.bar-right {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.price-info {
  text-align: right;
}

.total-info {
  margin-bottom: 8rpx;
}

.total-label {
  font-size: 28rpx;
  color: #666;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

.selected-count {
  font-size: 24rpx;
  color: #999;
}

.delivery-fee-info {
  font-size: 22rpx;
  color: #666;
  margin-left: 8rpx;
}

// 弹窗样式
.delete-modal,
.clear-modal {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 40rpx;
  width: 600rpx;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.modal-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

// 弹窗样式
.coupon-popup,
.delivery-popup {
  max-height: 70vh;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 40rpx;
    border-bottom: 1px solid #eee;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .popup-footer {
    display: flex;
    padding: 30rpx 40rpx;
    border-top: 1px solid #eee;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      padding: 20rpx 0;
      font-size: 28rpx;
      text-align: center;
      border-radius: 12rpx;
    }

    .cancel-btn {
      margin-right: 20rpx;
      color: #666;
      background-color: #f5f5f5;
    }

    .confirm-btn {
      color: #fff;
      background-color: #ff5500;
    }
  }
}

.coupon-list {
  max-height: 800rpx;
  padding: 0 40rpx;
}

.coupon-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.selected {
    background-color: #fff0e6;
  }

  .coupon-info {
    flex: 1;
    display: flex;
    align-items: center;

    .coupon-amount {
      width: 120rpx;
      margin-right: 30rpx;
      font-size: 40rpx;
      font-weight: 500;
      color: #ff5500;
      text-align: center;
    }

    .coupon-details {
      flex: 1;

      .coupon-name {
        margin-bottom: 6rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
      }

      .coupon-condition {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .coupon-select {
    margin-left: 20rpx;
  }
}

.no-coupons {
  padding: 80rpx 0;
  text-align: center;

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.delivery-options {
  padding: 0 40rpx;
}

.delivery-option {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.selected {
    background-color: #fff0e6;
  }

  .option-info {
    flex: 1;

    .option-name {
      margin-bottom: 6rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }

    .option-desc {
      margin-bottom: 6rpx;
      font-size: 24rpx;
      color: #666;
    }

    .option-time {
      font-size: 24rpx;
      color: #999;
    }
  }

  .option-fee {
    margin-right: 30rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #ff5500;
  }

  .option-select {
    margin-left: 20rpx;
  }
}

// 地址选择器样式
.address-section {
  margin: 10px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;

  .address-info {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .address-header {
      display: flex;
      align-items: flex-start;
      flex: 1;

      .address-details {
        margin-left: 10px;
        flex: 1;

        .address-name-phone {
          display: flex;
          align-items: center;
          margin-bottom: 5px;

          .name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-right: 10px;
          }

          .phone {
            font-size: 14px;
            color: #666;
          }
        }

        .address-text {
          font-size: 14px;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }

  .no-address {
    display: flex;
    align-items: center;
    justify-content: space-between;

    text {
      margin-left: 10px;
      font-size: 16px;
      color: #333;
    }
  }
}

// 地址选择弹窗样式
.address-popup {
  max-height: 80vh;
  min-height: 300px;
  background-color: #fff;
  border-radius: 12px 12px 0 0;
  position: relative;
  z-index: 10000;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10001;

    .popup-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .address-list {
    max-height: 50vh;
    min-height: 200px;
    padding: 0 20px;
    overflow-y: auto;
  }

  .address-item {
    display: flex;
    align-items: center;
    padding: 15px 10px;
    margin: 0 -10px;
    border-bottom: 1px solid #f5f5f5;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    z-index: 1;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8f8f8;
    }

    &.selected {
      background-color: #fff0e6;
      border: 1px solid #ff5500;
    }

    &:active {
      background-color: #f0f0f0;
    }

    .address-content {
      flex: 1;

      .address-header {
        display: flex;
        align-items: center;
        margin-bottom: 5px;

        .receiver {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-right: 10px;
        }

        .phone {
          font-size: 14px;
          color: #666;
          margin-right: 10px;
        }

        .default-tag {
          font-size: 12px;
          color: #ff5500;
          background-color: #fff0e6;
          padding: 2px 6px;
          border-radius: 2px;
        }
      }

      .address-detail {
        font-size: 14px;
        color: #666;
        line-height: 1.4;
      }
    }

    .address-selected {
      margin-left: 10px;
    }
  }

  .no-address {
    padding: 40px 0;
    text-align: center;

    text {
      font-size: 14px;
      color: #999;
    }
  }

  .popup-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    position: sticky;
    bottom: 0;
    background-color: #fff;
    z-index: 10001;

    .add-address-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 0;
      font-size: 14px;
      color: #ff5500;
      background-color: #fff;
      border: 1px solid #ff5500;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: #fff0e6;
      }

      &:active {
        background-color: #ffe6d9;
      }

      text {
        margin-left: 5px;
      }
    }
  }

  /* 无优惠信息提示样式 */
  .no-offers-tip {
    margin: 20rpx 0;
    padding: 24rpx 30rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    border-left: 6rpx solid #e9ecef;

    .tip-content {
      display: flex;
      align-items: center;
      justify-content: center;

      .tip-icon {
        font-size: 32rpx;
        margin-right: 16rpx;
      }

      .tip-text {
        font-size: 28rpx;
        color: #6c757d;
        line-height: 1.4;
      }
    }
  }
}
</style>
