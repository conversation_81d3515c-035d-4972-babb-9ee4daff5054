<template>
  <view class="device-management">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">设备管理</text>
      <text class="page-subtitle">管理您的登录设备</text>
    </view>

    <!-- 设备列表 -->
    <view class="device-list">
      <view
        v-for="device in deviceList"
        :key="device.device_id"
        class="device-item"
        :class="{ 'current-device': device.is_current }"
      >
        <!-- 设备图标和基本信息 -->
        <view class="device-info">
          <view class="device-icon">
            <text class="icon" :class="getDeviceIcon(device.device_type)"></text>
          </view>
          <view class="device-details">
            <view class="device-name">{{ device.device_name }}</view>
            <view class="device-meta">
              <text class="device-type">{{ getDeviceTypeText(device.device_type) }}</text>
              <text class="device-platform">{{ getPlatformText(device.platform) }}</text>
              <text class="device-browser">{{ device.browser }}</text>
            </view>
            <view class="device-time">最后登录：{{ formatTime(device.last_login_at) }}</view>
            <view v-if="device.location" class="device-location">
              <text class="location-icon">📍</text>
              {{ device.location }}
            </view>
          </view>
        </view>

        <!-- 设备状态标签 -->
        <view class="device-status">
          <view v-if="device.is_current" class="status-tag current">当前设备</view>
          <view v-if="device.is_trusted" class="status-tag trusted">受信任</view>
        </view>

        <!-- 设备操作按钮 -->
        <view class="device-actions">
          <button
            v-if="!device.is_current"
            class="action-btn trust-btn"
            :class="{ active: device.is_trusted }"
            @click="toggleTrust(device)"
          >
            {{ device.is_trusted ? '取消信任' : '设为信任' }}
          </button>
          <button
            v-if="!device.is_current"
            class="action-btn logout-btn"
            @click="logoutDevice(device)"
          >
            登出
          </button>
        </view>
      </view>
    </view>

    <!-- 批量操作 -->
    <view class="batch-actions">
      <button
        class="batch-btn logout-all-btn"
        @click="logoutAllOtherDevices"
        :disabled="otherDevicesCount === 0"
      >
        登出所有其他设备 ({{ otherDevicesCount }})
      </button>
    </view>

    <!-- 空状态 -->
    <view v-if="deviceList.length === 0" class="empty-state">
      <text class="empty-icon">📱</text>
      <text class="empty-text">暂无设备信息</text>
      <button class="refresh-btn" @click="loadDevices">刷新</button>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  getDeviceList,
  logoutDevice as apiLogoutDevice,
  logoutAllOtherDevices as apiLogoutAllOtherDevices,
  setTrustedDevice,
} from '@/api/login'
import { IDeviceResponse } from '@/api/login.typings'
import { toast } from '@/utils/toast'

// 响应式数据
const deviceList = ref<IDeviceResponse[]>([])
const loading = ref(false)

// 计算属性
const otherDevicesCount = computed(() => {
  return deviceList.value.filter((device) => !device.is_current).length
})

// 页面加载时获取设备列表
onMounted(() => {
  loadDevices()
})

// 加载设备列表
const loadDevices = async () => {
  loading.value = true
  try {
    const response = await getDeviceList()
    deviceList.value = response.data
    console.log('设备列表加载成功:', response.data)
  } catch (error) {
    console.error('加载设备列表失败:', error)
    toast.error('加载设备列表失败')
  } finally {
    loading.value = false
  }
}

// 登出指定设备
const logoutDevice = async (device: IDeviceResponse) => {
  uni.showModal({
    title: '确认登出',
    content: `确定要登出设备"${device.device_name}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await apiLogoutDevice(device.device_id)
          toast.success('设备登出成功')
          // 重新加载设备列表
          loadDevices()
        } catch (error) {
          console.error('登出设备失败:', error)
          toast.error('登出设备失败')
        }
      }
    },
  })
}

// 登出所有其他设备
const logoutAllOtherDevices = async () => {
  if (otherDevicesCount.value === 0) {
    toast.info('没有其他设备需要登出')
    return
  }

  uni.showModal({
    title: '确认登出',
    content: `确定要登出所有其他设备(${otherDevicesCount.value}台)吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await apiLogoutAllOtherDevices()
          toast.success('所有其他设备已登出')
          // 重新加载设备列表
          loadDevices()
        } catch (error) {
          console.error('登出所有其他设备失败:', error)
          toast.error('登出所有其他设备失败')
        }
      }
    },
  })
}

// 切换设备信任状态
const toggleTrust = async (device: IDeviceResponse) => {
  const newTrustedState = !device.is_trusted
  const action = newTrustedState ? '设为信任' : '取消信任'

  try {
    await setTrustedDevice(device.device_id, newTrustedState)
    toast.success(`${action}成功`)
    // 更新本地状态
    device.is_trusted = newTrustedState
  } catch (error) {
    console.error(`${action}失败:`, error)
    toast.error(`${action}失败`)
  }
}

// 获取设备图标
const getDeviceIcon = (deviceType: string): string => {
  const iconMap: Record<string, string> = {
    mobile: 'icon-phone',
    desktop: 'icon-desktop',
    tablet: 'icon-tablet',
    web: 'icon-browser',
  }
  return iconMap[deviceType] || 'icon-device'
}

// 获取设备类型文本
const getDeviceTypeText = (deviceType: string): string => {
  const typeMap: Record<string, string> = {
    mobile: '手机',
    desktop: '桌面',
    tablet: '平板',
    web: '网页',
  }
  return typeMap[deviceType] || deviceType
}

// 获取平台文本
const getPlatformText = (platform: string): string => {
  const platformMap: Record<string, string> = {
    ios: 'iOS',
    android: 'Android',
    windows: 'Windows',
    macos: 'macOS',
    linux: 'Linux',
    web: 'Web',
  }
  return platformMap[platform] || platform
}

// 格式化时间
const formatTime = (timeStr: string): string => {
  if (!timeStr) return '未知'

  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }

  // 超过7天，显示具体日期
  return time.toLocaleDateString()
}
</script>

<style lang="scss" scoped>
.device-management {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.page-header {
  text-align: center;
  padding: 40rpx 0;
  background: white;
  margin-bottom: 20rpx;
  border-radius: 16rpx;

  .page-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .page-subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.device-list {
  .device-item {
    background: white;
    margin-bottom: 20rpx;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    &.current-device {
      border: 2rpx solid #1989fa;
      background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
    }
  }
}

.device-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;

  .device-icon {
    width: 80rpx;
    height: 80rpx;
    background: #1989fa;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;

    .icon {
      font-size: 36rpx;
      color: white;

      &.icon-phone::before {
        content: '📱';
      }
      &.icon-desktop::before {
        content: '🖥️';
      }
      &.icon-tablet::before {
        content: '📱';
      }
      &.icon-browser::before {
        content: '🌐';
      }
      &.icon-device::before {
        content: '📟';
      }
    }
  }

  .device-details {
    flex: 1;

    .device-name {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .device-meta {
      display: flex;
      gap: 20rpx;
      margin-bottom: 10rpx;

      text {
        font-size: 24rpx;
        color: #666;
        background: #f0f0f0;
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
      }
    }

    .device-time {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 8rpx;
    }

    .device-location {
      font-size: 24rpx;
      color: #666;
      display: flex;
      align-items: center;

      .location-icon {
        margin-right: 8rpx;
      }
    }
  }
}

.device-status {
  display: flex;
  gap: 10rpx;
  margin-bottom: 20rpx;

  .status-tag {
    font-size: 22rpx;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;

    &.current {
      background: #1989fa;
      color: white;
    }

    &.trusted {
      background: #07c160;
      color: white;
    }
  }
}

.device-actions {
  display: flex;
  gap: 20rpx;

  .action-btn {
    flex: 1;
    height: 60rpx;
    border-radius: 12rpx;
    font-size: 26rpx;
    border: none;

    &.trust-btn {
      background: #f0f0f0;
      color: #333;

      &.active {
        background: #07c160;
        color: white;
      }
    }

    &.logout-btn {
      background: #ff4757;
      color: white;
    }
  }
}

.batch-actions {
  padding: 30rpx;

  .batch-btn {
    width: 100%;
    height: 80rpx;
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 16rpx;
    font-size: 30rpx;
    font-weight: bold;

    &:disabled {
      background: #ccc;
      color: #999;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;

  .empty-icon {
    font-size: 120rpx;
    display: block;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
    display: block;
    margin-bottom: 40rpx;
  }

  .refresh-btn {
    background: #1989fa;
    color: white;
    border: none;
    padding: 20rpx 40rpx;
    border-radius: 12rpx;
    font-size: 26rpx;
  }
}

.loading-state {
  text-align: center;
  padding: 60rpx 0;

  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
