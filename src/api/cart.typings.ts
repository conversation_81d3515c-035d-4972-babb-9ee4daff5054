/**
 * 购物车相关类型定义
 * 定义购物车商品等相关的数据结构
 */

/**
 * 商品规格变体
 */
export interface IProductVariant {
  id: number
  productId: number
  name: string
  description?: string
  image?: string
  price: number
  originalPrice: number
  stock: number
  isDefault: boolean
}

/**
 * 套餐选择项
 */
export interface IComboSelection {
  comboItemId: number
  comboItemName: string
  comboId: number
  comboName: string
  selectedOptions: IComboOptionSelection[]
}

/**
 * 套餐选项选择
 */
export interface IComboOptionSelection {
  optionId: number
  optionName: string
  extraPrice: number
  quantity: number
}

/**
 * 购物车商品项（扩展版）
 */
export interface ICartItem {
  id: number
  userId: number
  productId: number
  productTitle: string // 改名以匹配Vue组件
  productImage: string
  productPrice: number
  originalPrice?: number // 原价
  specificationId?: number
  specificationName?: string
  specifications?: Record<string, string> // 规格信息对象
  quantity: number
  selected: boolean
  merchantId: number
  merchantName: string
  merchantLongitude?: number
  merchantLatitude?: number
  merchantSupportPickup?: number // 商家是否支持到店自取：0-不支持，1-支持
  stock: number
  available: boolean // 商品是否可用

  // 外卖特有字段
  variantId?: number
  variantName?: string
  packagingFee?: number // 包装费
  subtotal: number // 小计
  comboSelections?: IComboSelection[] // 套餐选择
  remark?: string // 备注
  promotionInfo?: string // 促销信息
  promotions?: IPromotion[] // 促销活动列表

  createTime: string
  updateTime: string
}

/**
 * 促销活动
 */
export interface IPromotion {
  id: number
  name: string
  description: string
  type: number
  type_name: string
  rules: string
  start_time: string
  end_time: string
}

/**
 * 购物车列表响应（扩展版）
 */
export interface ICartList {
  list: ICartItem[]
  total: number
  selectedCount: number
  totalAmount: number
  selectedAmount: number
  totalQuantity: number // 总数量
  packagingFeeAmount?: number // 包装费总额
  deliveryFee?: number // 配送费
  discountAmount?: number // 优惠金额

  // 按商家分组的购物车
  merchantGroups?: IMerchantCartGroup[]
}

/**
 * 商家购物车分组
 */
export interface IMerchantCartGroup {
  merchantId: number
  merchantName: string
  merchantLogo?: string
  merchantLongitude?: number
  merchantLatitude?: number
  minOrderAmount: number
  deliveryFee: number
  items: ICartItem[]
  subtotal: number
  packagingFee: number
  selectedSubtotal: number // 选中商品小计
  selectedPackagingFee: number // 选中商品包装费
  canCheckout: boolean // 是否满足起送条件
  selectedCount: number // 选中商品数量
  totalCount: number // 总商品数量
  promotions: IPromotion[] // 商家促销活动
  couponId?: number // 选中的优惠券ID
  remark?: string // 商家备注
  supportPickup?: number // 是否支持到店自取：0-不支持，1-支持
  deliveryMethod?: 'delivery' | 'pickup' // 配送方式：delivery-配送，pickup-自取
}

/**
 * 添加到购物车参数（扩展版）
 */
export interface IAddToCartParams {
  productId: number
  quantity: number
  specificationId?: number

  // 外卖特有参数
  variantId?: number
  comboSelections?: IComboSelection[]
  remark?: string
}

/**
 * 更新购物车参数（扩展版）
 */
export interface IUpdateCartParams {
  cartItemId: number
  quantity?: number
  selected?: boolean
  remark?: string
}

/**
 * 购物车统计信息
 */
export interface ICartStats {
  totalItems: number
  totalQuantity: number
  selectedItems: number
  selectedQuantity: number
  unselectedItems: number
  unselectedQuantity: number
  totalAmount: number
  selectedAmount: number
  merchantCount: number
}
